import { app, shell, BrowserWindow, ipcMain, systemPreferences } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { GoogleGenAI } from '@google/genai'
import { getSystemPrompt, generateInterviewPrompt } from './prompts'
import { analyzeAudioBuffer, saveDebugAudio } from './audioUtils'
import { spawn, ChildProcess } from 'child_process'
import { exec } from 'child_process'
import { promisify } from 'util'
import { AudioStreamProcessor } from './services/AudioQueueManager'
import { AudioAnalysisProcessor } from './services/AudioProcessorAdapter'

// 导入完整的服务架构
import { PrivacyProtectionService } from './services/PrivacyProtectionService'
import { ServiceManager } from './services/ServiceManager'
import type { ServiceMode } from './services/ServiceTypes'
import { ServiceConfigManager } from './services/ServiceConfigManager'
import { CustomAPIManager } from './services/CustomAPIManager'
import { ScreenshotTestService } from './services/ScreenshotTestService'
import { InterviewOCRService } from './services/InterviewOCRService'


const execAsync = promisify(exec)

// 权限状态类型定义
interface PermissionStatus {
  granted: boolean
  canRequest: boolean
  message: string
}

interface SystemPermissions {
  screenRecording: PermissionStatus
  microphone: PermissionStatus
  apiKey: PermissionStatus
  audioDevice: PermissionStatus
}

let mainWindow: BrowserWindow | null = null
let floatingWindow: BrowserWindow | null = null
let geminiSession: any = null
let systemAudioProc: ChildProcess | null = null
let audioStreamProcessor: AudioStreamProcessor | null = null
let isInitializingSession = false
let reconnectAttempts = 0
let maxReconnectAttempts = 3
let reconnectTimeout: NodeJS.Timeout | null = null
let currentApiKey: string | null = ''
let currentCustomPrompt = ''
let currentProfile = 'interview'
let currentLanguage = 'cmn-CN'
let messageBuffer = ''
let currentTranscription = ''
let audioChunkCount = 0

// 完整的服务架构实例
let privacyService: PrivacyProtectionService | null = null
let serviceManager: ServiceManager | null = null
let serviceConfigManager: ServiceConfigManager | null = null
let customAPIManager: CustomAPIManager | null = null


/**
 * 初始化所有服务
 */
async function initializeServices(): Promise<void> {
  try {
    console.log('🚀 Initializing GeekAssistant services...')

    // 初始化隐私保护服务 - 开发模式下也启用隐身功能用于测试
    const isProduction = app.isPackaged
    privacyService = new PrivacyProtectionService({
      enableScreenCaptureProtection: isProduction,
      enableVisibilityProtection: isProduction,
      enableWindowInfoProtection: isProduction,
      enableAPIProtection: isProduction,
      stealthMode: isProduction,
      enableAdvancedStealth: true, // 开发模式下也启用隐身功能
      enableProcessHiding: isProduction,
      enableMemoryProtection: isProduction,
      enableGlobalEventProtection: true, // 启用全局事件保护，支持区域截图
      enableKeyboardProtection: false, // 开发模式下禁用键盘保护
      enableMouseProtection: true // 启用鼠标保护，支持区域选择
    })

    // 初始化隐私保护服务
    await privacyService.initialize()

    if (isProduction) {
      console.log('🔒 Privacy protection enabled (production mode)')
    } else {
      console.log('🔓 Privacy protection disabled (development mode)')
    }

    // 初始化服务配置管理器
    serviceConfigManager = ServiceConfigManager.getInstance()
    await serviceConfigManager.initialize()

    // 初始化自定义API管理器
    customAPIManager = CustomAPIManager.getInstance()
    await customAPIManager.initialize()

    // 音频捕获将根据平台使用不同方案：
    // macOS: SystemAudioDump (系统音频)
    // 其他平台: 暂不支持 (可以后续添加)

    // 初始化服务管理器
    serviceManager = ServiceManager.getInstance()
    await serviceManager.initialize()

    // 设置服务管理器事件监听
    setupServiceManagerEvents()

    console.log('🚀✅ All services initialized successfully')
  } catch (error) {
    console.error('🚀❌ Failed to initialize services:', error)
    throw error
  }
}

/**
 * 设置服务管理器事件监听
 */
function setupServiceManagerEvents(): void {
  if (!serviceManager) return

  serviceManager.on('transcription-received', (data) => {
    sendToRenderer('transcription-update', data.text)
    sendToRenderer('transcription-result', data)
  })

  serviceManager.on('ai-response-received', (data) => {
    sendToRenderer('ai-response', data.text || data.response)
  })

  serviceManager.on('status-changed', (status) => {
    sendToRenderer('update-status', status.status)
  })

  serviceManager.on('error-occurred', (error) => {
    sendToRenderer('session-error', error.error)
  })

  serviceManager.on('service-switched', (mode: ServiceMode) => {
    sendToRenderer('service-switched', mode)
  })
}

async function createWindow(): Promise<void> {
  // 初始化完整的服务架构
  await initializeServices()

  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 900,
    height: 650,
    show: false,
    autoHideMenuBar: true,
    title: 'Geek 助手 - AI面试伙伴',
    // ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.mjs'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // 🛡️ reverse-analysis的屏幕共享保护 - 完全按照源码实现
  mainWindow.setContentProtection(true)

  // 🛡️ 额外的截图保护措施 - 防止系统截图
  if (process.platform === 'darwin') {
    // macOS特定的截图保护
    mainWindow.setVisibleOnAllWorkspaces(false, { visibleOnFullScreen: false })
  }

  console.log('🛡️ Content protection enabled for main window - screen sharing blocked (reverse-analysis method)')

  // 应用隐私保护 - 开发模式下也应用隐身功能
  if (privacyService) {
    privacyService.applyWindowProtection(mainWindow, false)
    console.log('🔒 Window privacy protection applied to main window')
  } else {
    console.log('🔓 Window privacy protection skipped (no service)')
  }

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()
  })

  // 监听各种隐身请求
  mainWindow.webContents.on('ipc-message', (_event, channel, data) => {
    if (channel === 'toggle-mouse-passthrough-requested') {
      if (privacyService && mainWindow) {
        const result = privacyService.toggleMousePassthrough(mainWindow)
        console.log(`🖱️ Mouse passthrough toggled: ${result}`)
      }
    } else if (channel === 'apply-super-stealth-requested') {
      // 处理超级隐身请求
      const windowId = data;
      const targetWindow = windowId === mainWindow?.id ? mainWindow : floatingWindow;

      if (privacyService && targetWindow && privacyService.getAdvancedWindowHider) {
        const hider = privacyService.getAdvancedWindowHider();
        if (hider) {
          hider.applySuperStealth(targetWindow);
          console.log(`🕳️ Applied reverse-analysis super stealth to window ${windowId}`);
        }
      }
    }
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

function createFloatingWindow(): BrowserWindow {
  floatingWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    resizable: true,
    movable: true,
    minimizable: false,
    maximizable: false,
    skipTaskbar: true,
    webPreferences: {
      preload: join(__dirname, '../preload/index.mjs'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  // macOS 隐形功能 - 只在生产环境启用
  if (process.platform === 'darwin' && app.isPackaged) {
    // @ts-ignore - macOS specific API
    floatingWindow.setWindowButtonVisibility?.(false)
    // 设置窗口为不可捕获类型
    try {
      // @ts-ignore - macOS specific API
      floatingWindow.setVisibleOnAllWorkspaces?.(true, { visibleOnFullScreen: true })
      console.log('🔒 Floating window privacy protection applied')
    } catch (error) {
      console.log('macOS specific API not available:', error)
    }
  } else if (process.platform === 'darwin') {
    console.log('🔓 Floating window privacy protection skipped (development mode)')
  }

  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    floatingWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/#/floating')
  } else {
    floatingWindow.loadFile(join(__dirname, '../renderer/index.html'), { hash: '/floating' })
  }

  // 🛡️ reverse-analysis的屏幕共享保护 - 浮动窗口也需要保护
  floatingWindow.setContentProtection(true)

  // 🛡️ 额外的截图保护措施 - 防止系统截图
  if (process.platform === 'darwin') {
    // macOS特定的截图保护
    floatingWindow.setVisibleOnAllWorkspaces(false, { visibleOnFullScreen: false })
  }

  console.log('🛡️ Content protection enabled for floating window - screen sharing blocked (reverse-analysis method)')

  // 应用隐私保护到浮动窗口
  if (privacyService) {
    privacyService.applyWindowProtection(floatingWindow, true)
    console.log('🔒 Window privacy protection applied to floating window')
  }

  floatingWindow.on('closed', () => {
    floatingWindow = null
  })

  return floatingWindow
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.bready.app')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  stopSystemAudioCapture()
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  stopSystemAudioCapture()
  // 清理隐身服务
  if (privacyService) {
    privacyService.destroy()
  }
})

// In this file you can include the rest of your app"s main process code.
// You can also put them in separate files and require them here.

// 添加全局错误处理，避免控制台输出错误
process.on('uncaughtException', (error) => {
  // 忽略控制台输出相关的错误
  if (error.message && error.message.includes('write EIO')) {
    return // 静默处理控制台输出错误
  }
  console.error('Uncaught Exception:', error)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// IPC handlers

// 新的服务管理API
ipcMain.handle('initialize-services', async () => {
  try {
    if (!serviceManager) {
      await initializeServices()
    }
    return serviceManager?.isServiceReady() || false
  } catch (error) {
    console.error('Failed to initialize services:', error)
    return false
  }
})

ipcMain.handle('switch-service', async (event, config: any) => {
  void event;
  try {
    if (!serviceManager) {
      await initializeServices()
    }
    return await serviceManager!.switchToService(config)
  } catch (error) {
    console.error('Failed to switch service:', error)
    return false
  }
})

ipcMain.handle('start-session', async () => {
  try {
    if (!serviceManager) {
      return false
    }
    return await serviceManager.startSession()
  } catch (error) {
    console.error('Failed to start session:', error)
    return false
  }
})

ipcMain.handle('stop-session', async () => {
  try {
    if (serviceManager) {
      await serviceManager.stopSession()
    }
    return true
  } catch (error) {
    console.error('Failed to stop session:', error)
    return false
  }
})

ipcMain.handle('update-transcription-language', async (_, language: string) => {
  try {
    console.log('🌍 Updating transcription language to:', language)
    if (serviceManager) {
      await serviceManager.updateTranscriptionLanguage(language)
    }
    return true
  } catch (error) {
    console.error('Failed to update transcription language:', error)
    return false
  }
})

ipcMain.handle('start-audio-capture', async (_, audioSource = 'system') => {
  try {
    if (!serviceManager) {
      return false
    }

    if (process.platform === 'darwin') {
      if (audioSource === 'microphone') {
        console.log('🎤 Using microphone audio capture for macOS')
        const success = await startMicrophoneCapture()
        return success
      } else {
        console.log('🎤 Using SystemAudioDump for macOS system audio capture')
        const success = await startSystemAudioCapture()
        return success
      }
    } else {
      console.error('🎤❌ Audio capture not supported on this platform')
      return false
    }
  } catch (error) {
    console.error('Failed to start audio capture:', error)
    return false
  }
})

ipcMain.handle('stop-audio-capture', async () => {
  try {
    if (process.platform === 'darwin') {
      stopSystemAudioCapture()
    }
    return true
  } catch (error) {
    console.error('Failed to stop audio capture:', error)
    return false
  }
})

// 配置管理API
ipcMain.handle('get-current-service-config', async () => {
  try {
    if (serviceManager) {
      return serviceManager.getCurrentConfig()
    }
    return null
  } catch (error) {
    console.error('Failed to get current service config:', error)
    return null
  }
})

ipcMain.handle('update-service-config', async (event, config: any) => {
  void event;
  try {
    if (serviceManager) {
      const success = await serviceManager.updateConfig(config)
      if (success) {
        // 通知所有渲染进程配置已更新
        sendToRenderer('config-updated', config)
      }
      return success
    }
    return false
  } catch (error) {
    console.error('Failed to update service config:', error)
    return false
  }
})

ipcMain.handle('reload-service-config', async () => {
  try {
    if (serviceManager) {
      return await serviceManager.reloadConfig()
    }
    return null
  } catch (error) {
    console.error('Failed to reload service config:', error)
    return null
  }
})

// 自定义API管理
ipcMain.handle('custom-api-get-all', async () => {
  try {
    if (customAPIManager) {
      return customAPIManager.getAllAPIs()
    }
    return []
  } catch (error) {
    console.error('Failed to get custom APIs:', error)
    return []
  }
})

ipcMain.handle('custom-api-add', async (event, api: any) => {
  void event;
  try {
    if (customAPIManager) {
      const result = await customAPIManager.addAPI(api)
      return result !== null
    }
    return false
  } catch (error) {
    console.error('Failed to add custom API:', error)
    return false
  }
})

ipcMain.handle('custom-api-update', async (event, id: string, updates: any) => {
  void event;
  try {
    if (customAPIManager) {
      return await customAPIManager.updateAPI(id, updates)
    }
    return false
  } catch (error) {
    console.error('Failed to update custom API:', error)
    return false
  }
})

ipcMain.handle('custom-api-remove', async (event, id: string) => {
  void event;
  try {
    if (customAPIManager) {
      return await customAPIManager.removeAPI(id)
    }
    return false
  } catch (error) {
    console.error('Failed to remove custom API:', error)
    return false
  }
})

ipcMain.handle('custom-api-test-chat', async (event, id: string, message: string) => {
  void event;
  try {
    if (customAPIManager) {
      return await customAPIManager.testAPIChat(id, message)
    }
    return { success: false, message: 'Custom API manager not available' }
  } catch (error) {
    console.error('Failed to test custom API chat:', error)
    const message = error instanceof Error ? error.message : String(error)
    return { success: false, message }
  }
})

ipcMain.handle('enter-collaboration-mode', () => {
  try {
    console.log('Entering collaboration mode...')
    if (mainWindow) {
      // 调整主窗口大小为协作模式
      mainWindow.setSize(800, 600)
      mainWindow.center()
      console.log('Main window resized for collaboration mode')
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to enter collaboration mode:', error)
    return false
  }
})

ipcMain.handle('exit-collaboration-mode', () => {
  try {
    console.log('Exiting collaboration mode...')
    if (mainWindow) {
      // 恢复主窗口原始大小
      mainWindow.setSize(1200, 800)
      mainWindow.center()
      console.log('Main window restored to normal size')
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to exit collaboration mode:', error)
    return false
  }
})

// 保留原有的浮窗功能作为备用
ipcMain.handle('create-floating-window', () => {
  try {
    console.log('Creating floating window...')
    if (!floatingWindow) {
      const window = createFloatingWindow()
      console.log('Floating window created successfully:', !!window)
      return true
    } else {
      console.log('Floating window already exists')
      floatingWindow.show()
      floatingWindow.focus()
      return true
    }
  } catch (error) {
    console.error('Failed to create floating window:', error)
    return false
  }
})

ipcMain.handle('close-floating-window', () => {
  if (floatingWindow) {
    floatingWindow.close()
    floatingWindow = null
  }
  return true
})

ipcMain.handle('initialize-gemini', async (event, apiKey: string, customPrompt = '', profile = 'interview', language = 'cmn-CN') => {
  void event;
  return await initializeGeminiSession(apiKey, customPrompt, profile, language)
})



ipcMain.handle('reconnect-gemini', async () => {
  if (currentApiKey) {
    reconnectAttempts = 0 // 重置重连计数
    return await initializeGeminiSession(currentApiKey, currentCustomPrompt, currentProfile, currentLanguage)
  }
  return false
})

ipcMain.handle('disconnect-gemini', () => {
  console.log('Manual disconnect requested')
  if (geminiSession) {
    geminiSession.close()
    geminiSession = null
  }
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
    reconnectTimeout = null
  }
  reconnectAttempts = maxReconnectAttempts // 防止自动重连
  stopSystemAudioCapture() // 停止音频捕获
  sendToRenderer('session-closed')
  return true
})

ipcMain.handle('manual-reconnect', async () => {
  console.log('Manual reconnect requested')
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
    reconnectTimeout = null
  }
  reconnectAttempts = 0 // 重置重连计数

  if (currentApiKey) {
    const success = await initializeGeminiSession(currentApiKey, currentCustomPrompt, currentProfile, currentLanguage)
    if (success) {
      sendToRenderer('session-paused-silence', false)
      sendToRenderer('update-status', 'Manual reconnect successful')
    }
    return success
  }
  return false
})

// 权限相关的IPC处理器
ipcMain.handle('check-permissions', async () => {
  return await getAllPermissionsStatus()
})

ipcMain.handle('check-screen-recording-permission', async () => {
  return await checkScreenRecordingPermission()
})

ipcMain.handle('check-microphone-permission', async () => {
  return await checkMicrophonePermission()
})

ipcMain.handle('check-api-key-status', async () => {
  return await checkApiKeyStatus()
})

ipcMain.handle('check-audio-device-status', async () => {
  return await checkAudioDeviceStatus()
})

ipcMain.handle('open-system-preferences', async (event, pane: string) => {
  void event;
  return await openSystemPreferences(pane)
})

ipcMain.handle('test-audio-capture', async () => {
  return await testAudioCapture()
})

// 面试上下文设置
let currentInterviewContext: any = null

ipcMain.handle('set-interview-context', async (event, preparation) => {
  void event;
  try {
    currentInterviewContext = preparation
    console.log('🎯 Interview context set:', preparation?.title || 'Quick Start')

    // 如果服务管理器存在，更新AI提示词上下文
    if (serviceManager) {
      const customPrompt = generateInterviewPrompt(preparation)
      await serviceManager.updateCustomPrompt(customPrompt)
      console.log('🎯 AI prompt updated with interview context')
    }

    return true
  } catch (error) {
    console.error('🎯❌ Failed to set interview context:', error)
    return false
  }
})

ipcMain.handle('get-interview-context', async () => {
  return currentInterviewContext
})

// 隐身模式相关的IPC处理器
ipcMain.handle('get-stealth-status', () => {
  try {
    if (privacyService) {
      return privacyService.getStealthStatus()
    }
    return null
  } catch (error) {
    console.error('Failed to get stealth status:', error)
    return null
  }
})

ipcMain.handle('toggle-main-window-stealth', () => {
  try {
    const stealthService = privacyService?.getStealthService()
    if (stealthService) {
      stealthService.toggleMainWindowStealth()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to toggle main window stealth:', error)
    return false
  }
})

ipcMain.handle('toggle-floating-window-stealth', () => {
  try {
    const stealthService = privacyService?.getStealthService()
    if (stealthService) {
      stealthService.toggleFloatingWindowStealth()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to toggle floating window stealth:', error)
    return false
  }
})

ipcMain.handle('toggle-overall-visibility', () => {
  try {
    const stealthService = privacyService?.getStealthService()
    if (stealthService) {
      stealthService.toggleOverallVisibility()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to toggle overall visibility:', error)
    return false
  }
})

ipcMain.handle('toggle-super-stealth-mode', () => {
  try {
    const stealthService = privacyService?.getStealthService()
    if (stealthService) {
      stealthService.toggleSuperStealthMode()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to toggle super stealth mode:', error)
    return false
  }
})

// 反检测相关的IPC处理器
ipcMain.handle('get-anti-detection-status', () => {
  try {
    if (privacyService) {
      return privacyService.getAntiDetectionStatus()
    }
    return null
  } catch (error) {
    console.error('Failed to get anti-detection status:', error)
    return null
  }
})

ipcMain.handle('toggle-mouse-passthrough', () => {
  try {
    if (privacyService && mainWindow) {
      return privacyService.toggleMousePassthrough(mainWindow)
    }
    return false
  } catch (error) {
    console.error('Failed to toggle mouse passthrough:', error)
    return false
  }
})

ipcMain.handle('toggle-floating-mouse-passthrough', () => {
  try {
    if (privacyService && floatingWindow) {
      return privacyService.toggleMousePassthrough(floatingWindow)
    }
    return false
  } catch (error) {
    console.error('Failed to toggle floating mouse passthrough:', error)
    return false
  }
})

// 性能和安全相关的IPC处理器
ipcMain.handle('get-performance-report', () => {
  try {
    if (privacyService) {
      return privacyService.getPerformanceReport()
    }
    return null
  } catch (error) {
    console.error('Failed to get performance report:', error)
    return null
  }
})

ipcMain.handle('get-optimization-suggestions', () => {
  try {
    if (privacyService) {
      return privacyService.getOptimizationSuggestions()
    }
    return []
  } catch (error) {
    console.error('Failed to get optimization suggestions:', error)
    return []
  }
})

ipcMain.handle('get-security-report', () => {
  try {
    if (privacyService) {
      return privacyService.getSecurityReport()
    }
    return null
  } catch (error) {
    console.error('Failed to get security report:', error)
    return null
  }
})

ipcMain.handle('get-compatibility-info', () => {
  try {
    if (privacyService) {
      return privacyService.getCompatibilityInfo()
    }
    return null
  } catch (error) {
    console.error('Failed to get compatibility info:', error)
    return null
  }
})

// 高级窗口隐藏相关的IPC处理器
ipcMain.handle('apply-reverse-analysis-stealth', () => {
  try {
    const hider = privacyService?.getAdvancedWindowHider()
    if (hider && mainWindow) {
      hider.applySuperStealth(mainWindow)
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to apply reverse-analysis stealth:', error)
    return false
  }
})

ipcMain.handle('restore-window-visibility', () => {
  try {
    const hider = privacyService?.getAdvancedWindowHider()
    if (hider && mainWindow) {
      hider.restoreWindow(mainWindow)
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to restore window visibility:', error)
    return false
  }
})

ipcMain.handle('get-hidden-windows-status', () => {
  try {
    const hider = privacyService?.getAdvancedWindowHider()
    if (hider) {
      return hider.getHiddenWindowsStatus()
    }
    return { count: 0, windowIds: [] }
  } catch (error) {
    console.error('Failed to get hidden windows status:', error)
    return { count: 0, windowIds: [] }
  }
})

// 全局事件保护相关的IPC处理器
ipcMain.handle('get-global-event-protection-status', () => {
  try {
    if (privacyService) {
      return privacyService.getGlobalEventProtectionStatus()
    }
    return null
  } catch (error) {
    console.error('Failed to get global event protection status:', error)
    return null
  }
})

ipcMain.handle('stop-keyboard-protection', () => {
  try {
    const protector = privacyService?.getGlobalEventProtector()
    if (protector) {
      protector.stopKeyboardProtection()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to stop keyboard protection:', error)
    return false
  }
})

ipcMain.handle('stop-mouse-protection', () => {
  try {
    const protector = privacyService?.getGlobalEventProtector()
    if (protector) {
      protector.stopMouseProtection()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to stop mouse protection:', error)
    return false
  }
})

// reverse-analysis的鼠标穿透IPC处理器
ipcMain.handle('enable-click-through', () => {
  try {
    const stealthService = privacyService?.getStealthService()
    if (stealthService && mainWindow) {
      // 直接调用enableClickThrough
      mainWindow.setIgnoreMouseEvents(true, { forward: true })
      console.log('🖱️ Click-through enabled via IPC (reverse-analysis method)')
      return { success: true }
    }
    return { error: 'Stealth service not available' }
  } catch (error) {
    console.error('Failed to enable click-through:', error)
    return { error: 'Failed to enable click-through' }
  }
})

ipcMain.handle('disable-click-through', () => {
  try {
    const stealthService = privacyService?.getStealthService()
    if (stealthService && mainWindow) {
      // 直接调用disableClickThrough
      mainWindow.setIgnoreMouseEvents(false)
      // 🛡️ 保持屏幕共享保护
      mainWindow.setContentProtection(true)
      if (!mainWindow.isFocused()) {
        mainWindow.focus()
      }
      console.log('🖱️ Click-through disabled via IPC (reverse-analysis method)')
      console.log('🛡️ Content protection maintained - screen sharing protection still active')
      return { success: true }
    }
    return { error: 'Stealth service not available' }
  } catch (error) {
    console.error('Failed to disable click-through:', error)
    return { error: 'Failed to disable click-through' }
  }
})

// reverse-analysis的屏幕共享保护控制
ipcMain.handle('enable-content-protection', () => {
  try {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.setContentProtection(true)
      console.log('🛡️ Content protection enabled - screen sharing blocked (reverse-analysis method)')
      return { success: true }
    }
    return { error: 'Main window not available' }
  } catch (error) {
    console.error('Failed to enable content protection:', error)
    return { error: 'Failed to enable content protection' }
  }
})

ipcMain.handle('disable-content-protection', () => {
  try {
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.setContentProtection(false)
      console.log('🛡️ Content protection disabled - screen sharing allowed')
      return { success: true }
    }
    return { error: 'Main window not available' }
  } catch (error) {
    console.error('Failed to disable content protection:', error)
    return { error: 'Failed to disable content protection' }
  }
})

ipcMain.handle('get-content-protection-status', () => {
  try {
    if (mainWindow && !mainWindow.isDestroyed()) {
      // Electron没有直接获取contentProtection状态的API，我们假设默认是启用的
      return { enabled: true, supported: true }
    }
    return { enabled: false, supported: false }
  } catch (error) {
    console.error('Failed to get content protection status:', error)
    return { enabled: false, supported: false }
  }
})

// 🧪 截图测试相关的IPC处理器
let screenshotTestService: ScreenshotTestService | null = null

// 🎯 面试OCR服务
let interviewOCRService: InterviewOCRService | null = null

ipcMain.handle('test-screenshot-recognition', async () => {
  try {
    if (!screenshotTestService) {
      screenshotTestService = new ScreenshotTestService()
    }

    console.log('🧪 Starting screenshot recognition test...')
    const result = await screenshotTestService.runFullTest()
    console.log('🧪 Test result:', result)

    return result
  } catch (error) {
    console.error('❌ Screenshot test failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('test-image-file', async (event, imagePath: string) => {
  void event;
  try {
    if (!screenshotTestService) {
      screenshotTestService = new ScreenshotTestService()
    }

    console.log('🧪 Testing image file:', imagePath)
    const result = await screenshotTestService.testImageFile(imagePath)
    console.log('🧪 Image test result:', result)

    return result
  } catch (error) {
    console.error('❌ Image file test failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('test-area-screenshot', async () => {
  try {
    if (!screenshotTestService) {
      screenshotTestService = new ScreenshotTestService()
    }

    console.log('🎯 Starting area screenshot test...')
    const result = await screenshotTestService.testAreaScreenshot()
    console.log('🎯 Area screenshot test result:', result)

    return result
  } catch (error) {
    console.error('❌ Area screenshot test failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

// 🎯 处理区域选择点击事件
ipcMain.on('area-selection-click', (event, data) => {
  void event;
  try {
    // 优先使用面试OCR服务处理区域选择
    if (interviewOCRService) {
      const handled = interviewOCRService.handleAreaSelectionClick(data.x, data.y)
      if (handled) {
        console.log(`🎯 Interview OCR area selection click handled: (${data.x}, ${data.y})`)
        return
      }
    }

    // 备选：使用截图测试服务
    if (screenshotTestService) {
      const handled = screenshotTestService.handleAreaSelectionClick(data.x, data.y)
      if (handled) {
        console.log(`🎯 Screenshot test area selection click handled: (${data.x}, ${data.y})`)
      }
    }
  } catch (error) {
    console.error('❌ Failed to handle area selection click:', error)
  }
})

// 🎯 处理面试OCR快捷键事件
ipcMain.on('trigger-interview-ocr', async (event, data) => {
  void event;
  try {
    if (!interviewOCRService) {
      interviewOCRService = new InterviewOCRService()
    }

    const { type } = data
    console.log(`🎯 Interview OCR shortcut triggered: ${type}`)

    let result
    if (type === 'fullscreen') {
      result = await interviewOCRService.quickFullscreenAnalysis()
    } else if (type === 'area') {
      result = await interviewOCRService.quickAreaAnalysis()
    }

    console.log('🎯 Interview OCR result:', result)

    // 发送结果到渲染进程
    if (result && mainWindow) {
      console.log('🎯 Sending result to renderer process...')
      mainWindow.webContents.send('interview-ocr-result', {
        type,
        result
      })
      console.log('✅ Result sent to renderer process')
    } else {
      console.warn('⚠️ Cannot send result: mainWindow or result is null')
    }
  } catch (error) {
    console.error('❌ Failed to handle interview OCR shortcut:', error)
  }
})

// 🔍 本地OCR测试相关的IPC处理器
ipcMain.handle('test-local-ocr', async () => {
  try {
    if (!screenshotTestService) {
      screenshotTestService = new ScreenshotTestService()
    }

    console.log('🔍 Starting local OCR test...')
    const result = await screenshotTestService.testLocalOCRAnalysis()
    console.log('🔍 Local OCR test result:', result)

    return result
  } catch (error) {
    console.error('❌ Local OCR test failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('test-local-ocr-area', async () => {
  try {
    if (!screenshotTestService) {
      screenshotTestService = new ScreenshotTestService()
    }

    console.log('🎯 Starting local OCR area test...')
    const result = await screenshotTestService.testLocalOCRAreaAnalysis()
    console.log('🎯 Local OCR area test result:', result)

    return result
  } catch (error) {
    console.error('❌ Local OCR area test failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

// 🎯 面试OCR相关的IPC处理器
ipcMain.handle('interview-ocr-fullscreen', async () => {
  try {
    if (!interviewOCRService) {
      interviewOCRService = new InterviewOCRService()
    }

    console.log('🎯 Starting interview OCR fullscreen analysis...')
    const result = await interviewOCRService.quickFullscreenAnalysis()
    console.log('🎯 Interview OCR fullscreen result:', result)

    return result
  } catch (error) {
    console.error('❌ Interview OCR fullscreen failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('interview-ocr-area', async () => {
  try {
    if (!interviewOCRService) {
      interviewOCRService = new InterviewOCRService()
    }

    console.log('🎯 Starting interview OCR area analysis...')
    const result = await interviewOCRService.quickAreaAnalysis()
    console.log('🎯 Interview OCR area result:', result)

    // 直接发送到渲染进程
    if (result && mainWindow) {
      console.log('🎯 Sending area result to renderer process via IPC...')
      mainWindow.webContents.send('interview-ocr-result', {
        type: 'area',
        result
      })
      console.log('✅ Area result sent to renderer process')
    }

    return result
  } catch (error) {
    console.error('❌ Interview OCR area failed:', error)
    const errorResult = {
      success: false,
      error: error.message
    }

    // 也发送错误结果
    if (mainWindow) {
      mainWindow.webContents.send('interview-ocr-result', {
        type: 'area',
        result: errorResult
      })
    }

    return errorResult
  }
})

// 🧪 测试IPC通信
ipcMain.handle('test-ipc-communication', async () => {
  try {
    console.log('🧪 Testing IPC communication...')

    if (mainWindow) {
      console.log('🧪 Sending test IPC event...')
      mainWindow.webContents.send('test-ipc', { message: 'Hello from main process!' })
      console.log('🧪 Test IPC event sent successfully')

      // 模拟一个面试OCR结果
      const mockResult = {
        success: true,
        category: '测试题目',
        quickAnswer: '这是一个测试回答',
        keyPoints: ['测试要点1', '测试要点2', '测试要点3'],
        ocrText: '这是测试的OCR文字内容'
      }

      console.log('🧪 Sending mock interview OCR result...')
      mainWindow?.webContents.send('interview-ocr-result', {
        type: 'test',
        result: mockResult
      })
      console.log('🧪 Mock interview OCR result sent successfully')

      // 等待一秒后再发送一个真实格式的结果
      setTimeout(() => {
        console.log('🧪 Sending realistic mock result...')
        mainWindow?.webContents.send('interview-ocr-result', {
          type: 'area',
          result: {
            success: true,
            category: '算法题',
            quickAnswer: '这是一个二分查找算法题，时间复杂度O(log n)',
            keyPoints: ['使用二分查找', '时间复杂度O(log n)', '需要有序数组'],
            ocrText: '给定一个有序数组，查找目标值的位置'
          }
        })
        console.log('🧪 Realistic mock result sent')
      }, 1000)

      return { success: true, message: 'Test IPC events sent' }
    } else {
      return { success: false, error: 'Main window not available' }
    }
  } catch (error) {
    console.error('❌ Test IPC communication failed:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

ipcMain.handle('request-microphone-permission', async () => {
  try {
    const granted = await systemPreferences.askForMediaAccess('microphone')
    return {
      granted,
      message: granted ? '麦克风权限已授予' : '麦克风权限被拒绝'
    }
  } catch (error) {
    return {
      granted: false,
      message: `请求麦克风权限失败: ${error.message}`
    }
  }
})

// 🔄 获取OCR服务状态
ipcMain.handle('get-ocr-status', async () => {
  try {
    if (!interviewOCRService) {
      interviewOCRService = new InterviewOCRService()
    }

    console.log('🔄 Getting OCR service status...')
    const status = await interviewOCRService.getOCRStatus()
    console.log('🔄 OCR status:', status)

    return {
      success: true,
      ...status
    }
  } catch (error) {
    console.error('❌ Failed to get OCR status:', error)
    return {
      success: false,
      error: error.message,
      currentMode: 'local-ocr',
      localOCRAvailable: true,
      remoteAPIAvailable: false
    }
  }
})

// 🧪 诊断远程API问题
ipcMain.handle('diagnose-remote-api', async () => {
  try {
    const { ReverseAnalysisService } = require('./services/ReverseAnalysisService')
    const service = new ReverseAnalysisService()

    console.log('🧪 Starting remote API diagnosis...')
    const diagnosis = await service.diagnoseAPIIssues()

    return {
      success: true,
      ...diagnosis
    }
  } catch (error) {
    console.error('❌ Failed to diagnose remote API:', error)
    return {
      success: false,
      error: error.message
    }
  }
})

async function initializeGeminiSession(apiKey: string, customPrompt = '', profile = 'interview', language = 'cmn-CN'): Promise<boolean> {
  if (isInitializingSession) {
    console.log('Session initialization already in progress')
    return false
  }

  console.log('Initializing Gemini session with:', {
    apiKeyLength: apiKey?.length || 0,
    profile,
    language,
    customPromptLength: customPrompt?.length || 0
  })

  // 验证API密钥
  if (!apiKey || apiKey.trim() === '') {
    console.error('Invalid API key provided')
    sendToRenderer('session-error', 'API密钥无效或为空')
    return false
  }

  // 保存当前配置用于重连
  currentApiKey = apiKey
  currentCustomPrompt = customPrompt
  currentProfile = profile
  currentLanguage = language

  isInitializingSession = true
  sendToRenderer('session-initializing', true)

  // 清除之前的重连定时器
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
    reconnectTimeout = null
  }

  try {
    console.log('Creating GoogleGenAI client...')
    const client = new GoogleGenAI({
      apiKey: apiKey,
    })

    const systemPrompt = getSystemPrompt(profile, customPrompt, false)
    console.log('System prompt generated, length:', systemPrompt.length)
    console.log('Connecting to Gemini Live API...')
    const session = await client.live.connect({
      model: 'gemini-live-2.5-flash-preview',
      callbacks: {
        onopen: function () {
          console.log('Gemini session opened successfully')
          reconnectAttempts = 0 // 重置重连计数
          sendToRenderer('update-status', 'Connected to Gemini - Starting recording...')
        },
        onmessage: function (message: any) {
          console.log('----------------', message)

          // Handle transcription input - 完全按照 cheatingdaddy 的方式
          if (message.serverContent?.inputTranscription?.text) {
            currentTranscription += message.serverContent.inputTranscription.text
            // 立即发送转录片段到前端，就像 cheatingdaddy 一样
            sendToRenderer('transcription-update', currentTranscription)
          }

          // Handle AI model response - 完全按照 cheatingdaddy 的方式
          if (message.serverContent?.modelTurn?.parts) {
            for (const part of message.serverContent.modelTurn.parts) {
              console.log('AI Part:', part)
              if (part.text) {
                messageBuffer += part.text
              }
            }
          }

          // 当生成完成时发送完整的 AI 回复 - 完全按照 cheatingdaddy 的方式
          if (message.serverContent?.generationComplete) {
            console.log('Generation Complete - AI Response:', messageBuffer)
            if (messageBuffer.trim()) {
              console.log('🤖 Sending AI response to frontend:', messageBuffer)
              sendToRenderer('ai-response', messageBuffer)

              // 保存对话记录
              if (currentTranscription && messageBuffer) {
                console.log('Saving conversation turn:', { transcription: currentTranscription, response: messageBuffer })
                currentTranscription = '' // 重置转录
              }
            }
            messageBuffer = '' // 重置消息缓冲区
          }

          // 处理对话轮次完成
          if (message.serverContent?.turnComplete) {
            sendToRenderer('update-status', 'Listening...')
          }
        },
        onerror: function (error: any) {
          console.error('Gemini session error:', error)
          const errorMessage = error.message || error.toString() || 'Unknown error'

          // 简化错误处理，直接报告错误
          sendToRenderer('session-error', `Gemini API 连接错误: ${errorMessage}`)

          // 检查是否是认证错误，如果是则停止重连
          if (errorMessage.includes('API key') || errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
            console.log('Authentication error detected - stopping reconnection attempts')
            currentApiKey = null
            reconnectAttempts = maxReconnectAttempts
            return
          }

          // 其他错误尝试重连
          if (reconnectAttempts < maxReconnectAttempts) {
            scheduleReconnect()
          }
        },
        onclose: function (e) {
          console.log('Gemini session closed:', e?.reason || 'Unknown reason')
          geminiSession = null
          sendToRenderer('session-closed')

          const reason = e?.reason || ''

          // 检查是否是配置错误（语言、认证等）
          if (reason.includes('language') || reason.includes('API key') || reason.includes('authentication') || reason.includes('unauthorized')) {
            console.log('Session closed due to configuration error:', reason)
            currentApiKey = null
            reconnectAttempts = maxReconnectAttempts
            sendToRenderer('session-error', `配置错误: ${reason}`)
            return
          }

          // 其他情况尝试重连
          if (reconnectAttempts < maxReconnectAttempts && currentApiKey && !isInitializingSession) {
            console.log('Session closed unexpectedly, scheduling reconnect...')
            scheduleReconnect()
          } else {
            sendToRenderer('update-status', 'Session closed')
          }
        }
      },
      config: {
        responseModalities: ['text' as any],
        inputAudioTranscription: {}, // 启用音频转录
        contextWindowCompression: { slidingWindow: {} },
        speechConfig: { languageCode: currentLanguage },
        systemInstruction: {
          parts: [{ text: systemPrompt }],
        },
      },
    })

    geminiSession = session
    isInitializingSession = false
    sendToRenderer('session-initializing', false)
    console.log('Gemini session initialized successfully')
    return true
  } catch (error: any) {
    console.error('Failed to initialize Gemini session:', error)

    let errorMessage = 'Unknown error'
    if (error.message) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    } else if (error.toString) {
      errorMessage = error.toString()
    }

    // 检查常见错误类型
    if (errorMessage.includes('API_KEY_INVALID') || errorMessage.includes('401') || errorMessage.includes('API key')) {
      errorMessage = 'API密钥无效，请检查.env.local文件中的VITE_GEMINI_API_KEY配置'
      currentApiKey = null // 清除无效的API密钥
    } else if (errorMessage.includes('PERMISSION_DENIED') || errorMessage.includes('403')) {
      errorMessage = 'API权限被拒绝，请检查API密钥权限'
    } else if (errorMessage.includes('language') || errorMessage.includes('Language')) {
      errorMessage = '语言配置错误，已自动修复为支持的语言代码'
    } else if (errorMessage.includes('NETWORK') || errorMessage.includes('fetch')) {
      errorMessage = '网络连接错误，请检查网络连接'
    }

    console.error('Detailed error:', errorMessage)
    isInitializingSession = false
    sendToRenderer('session-initializing', false)
    sendToRenderer('session-error', errorMessage)
    return false
  }
}

function sendToRenderer(channel: string, data?: any) {
  const windows = BrowserWindow.getAllWindows()
  windows.forEach(window => {
    window.webContents.send(channel, data)
  })
}

function scheduleReconnect() {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
  }

  // 检查是否应该重连
  if (!currentApiKey || isInitializingSession) {
    console.log('Skipping reconnect: no API key or already initializing')
    return
  }

  reconnectAttempts++
  const delay = Math.min(1000 * Math.pow(2, reconnectAttempts - 1), 30000) // 指数退避，最大30秒

  console.log(`Scheduling reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} in ${delay}ms`)
  sendToRenderer('update-status', `Connection lost. Reconnecting in ${Math.ceil(delay / 1000)}s... (${reconnectAttempts}/${maxReconnectAttempts})`)

  reconnectTimeout = setTimeout(async () => {
    if (reconnectAttempts > maxReconnectAttempts) {
      console.log('Maximum reconnect attempts reached')
      sendToRenderer('session-error', 'Failed to reconnect after maximum attempts')
      return
    }

    console.log(`Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts})`)
    sendToRenderer('update-status', 'Reconnecting...')

    try {
      if (!currentApiKey) return
      const success = await initializeGeminiSession(currentApiKey, currentCustomPrompt, currentProfile, currentLanguage)
      if (!success) {
        console.log('Reconnect failed, will retry if attempts remaining')
        if (reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        } else {
          sendToRenderer('session-error', 'Failed to reconnect after maximum attempts')
        }
      } else {
        console.log('Reconnect successful')
        reconnectAttempts = 0 // 重置重连计数
      }
    } catch (error) {
      console.error('Error during reconnect:', error)
      if (reconnectAttempts < maxReconnectAttempts) {
        scheduleReconnect()
      } else {
        sendToRenderer('session-error', 'Failed to reconnect after maximum attempts')
      }
    }
  }, delay)
}

async function startSystemAudioCapture(): Promise<boolean> {
  if (process.platform === 'darwin') {
    return await startMacOSAudioCapture()
  } else {
    console.log('System audio capture not implemented for this platform')
    sendToRenderer('session-error', 'System audio capture not supported on this platform')
    return false
  }
}

async function killExistingSystemAudioDump(): Promise<void> {
  return new Promise((resolve) => {
    console.log('Checking for existing SystemAudioDump processes...')

    // Kill any existing SystemAudioDump processes
    const killProc = spawn('pkill', ['-f', 'SystemAudioDump'], {
      stdio: 'ignore',
    })

    killProc.on('close', (code) => {
      if (code === 0) {
        console.log('Killed existing SystemAudioDump processes')
      } else {
        console.log('No existing SystemAudioDump processes found')
      }
      resolve()
    })

    killProc.on('error', (err) => {
      console.log('Error killing SystemAudioDump processes:', err)
      resolve()
    })

    // Timeout after 5 seconds
    setTimeout(() => {
      killProc.kill()
      resolve()
    }, 5000)
  })
}

async function startMacOSAudioCapture(): Promise<boolean> {
  if (process.platform !== 'darwin') return false

  // Kill any existing SystemAudioDump processes first
  await killExistingSystemAudioDump()

  console.log('Starting macOS audio capture with SystemAudioDump...')

  let systemAudioPath: string
  if (app.isPackaged) {
    systemAudioPath = join(process.resourcesPath, 'SystemAudioDump')
  } else {
    // 在开发环境中，我们需要提供 SystemAudioDump 的路径
    systemAudioPath = join(__dirname, '../../assets/SystemAudioDump')
  }

  console.log('SystemAudioDump path:', systemAudioPath)

  try {
    // SystemAudioDump 不支持命令行参数，直接启动
    systemAudioProc = spawn(systemAudioPath, [], {
      stdio: ['ignore', 'pipe', 'pipe'],
    })

    if (!systemAudioProc.pid) {
      console.error('Failed to start SystemAudioDump')
      return false
    }

    console.log('SystemAudioDump started with PID:', systemAudioProc.pid)

    // 初始化音频流处理器（高效队列系统）
    audioStreamProcessor = new AudioStreamProcessor(200) // 200个音频块的队列容量

    // 获取目标音频参数
    let targetSampleRate = 16000 // 默认16kHz

    if (serviceManager) {
      const audioRequirements = serviceManager.getAudioRequirements()
      if (audioRequirements) {
        targetSampleRate = audioRequirements.sampleRate || 16000
        console.log('🎤 Using service audio requirements:', audioRequirements)
      }
    }

    // 创建复合音频处理器（简化处理链）
    const analysisProcessor = new AudioAnalysisProcessor(15000) // 15秒分析一次，减少日志

    // 创建一个集成处理器，直接处理音频格式转换和发送
    const integratedProcessor = {
      async processAudio(audioData: Buffer): Promise<void> {
        try {
          let processedData = audioData

          // 转换立体声到单声道（SystemAudioDump输出是立体声）
          if (processedData.length > 0) {
            const monoBuffer = Buffer.alloc(processedData.length / 2)
            for (let i = 0; i < processedData.length; i += 4) {
              const left = processedData.readInt16LE(i)
              const right = processedData.readInt16LE(i + 2)
              const mono = Math.round((left + right) / 2)
              monoBuffer.writeInt16LE(mono, i / 2)
            }
            processedData = monoBuffer
          }

          // 重采样（24kHz -> 16kHz）
          if (processedData.length > 0 && targetSampleRate !== 24000) {
            const inputSamples = processedData.length / 2
            const outputSamples = Math.floor(inputSamples * targetSampleRate / 24000)
            const outputBuffer = Buffer.alloc(outputSamples * 2)
            const ratio = inputSamples / outputSamples

            for (let i = 0; i < outputSamples; i++) {
              const inputIndex = i * ratio
              const inputIndexFloor = Math.floor(inputIndex)
              const inputIndexCeil = Math.min(inputIndexFloor + 1, inputSamples - 1)

              const sample1 = processedData.readInt16LE(inputIndexFloor * 2)
              const sample2 = processedData.readInt16LE(inputIndexCeil * 2)

              const fraction = inputIndex - inputIndexFloor
              const interpolatedSample = Math.round(sample1 + (sample2 - sample1) * fraction)

              outputBuffer.writeInt16LE(interpolatedSample, i * 2)
            }
            processedData = outputBuffer
          }

          // 直接发送到服务管理器
          if (serviceManager && processedData.length > 0) {
            await serviceManager.sendAudio(processedData)
          }
        } catch (error) {
          console.error('🎵❌ Integrated audio processor error:', error)
        }
      }
    }

    // 添加处理器到流处理器
    audioStreamProcessor.addProcessor(integratedProcessor)
    audioStreamProcessor.addProcessor(analysisProcessor)

    // 启动音频流处理器
    audioStreamProcessor.start()
    console.log('🎵🚀 Audio stream processor with processing chain initialized and started')

    systemAudioProc.stdout?.on('data', (data: Buffer) => {
      audioChunkCount++

      // 直接将原始音频数据送入队列系统（生产者）
      if (audioStreamProcessor && audioStreamProcessor.getStatus().isActive) {
        const success = audioStreamProcessor.receiveAudio(data)

        if (!success) {
          console.warn('🎵⚠️ Failed to enqueue audio data, processor may be overloaded')
        }

        // 定期输出队列状态（用于监控）- 减少频率
        if (audioChunkCount % 5000 === 0) {
          const status = audioStreamProcessor.getStatus()
          console.log(`🎵📊 Audio Queue Status: ${status.queueStats.queueUtilization} utilization, ${status.estimatedLatency}ms latency`)
        }
      } else {
        console.warn('🎵⚠️ Audio stream processor not available, dropping audio data')
      }

      // 兼容旧的Gemini Live模式（直接处理）
      if (geminiSession) {
        // 对于Gemini Live，我们需要进行格式转换
        const processedChunk = stereoToMono(data)
        sendAudioToGemini(processedChunk.toString('base64'))
      }

      // 定期保存调试音频（开发模式）
      if (process.env.NODE_ENV === 'development' && Math.random() < 0.01) {
        saveDebugAudio(data, 'raw_capture')
      }
    })

    systemAudioProc.stderr?.on('data', (data: Buffer) => {
      const errorMsg = data.toString()
      // 只记录重要的错误信息，忽略正常的状态消息
      if (!errorMsg.includes('Capturing system audio') && !errorMsg.includes('Press ⌃C to stop')) {
        console.error('SystemAudioDump stderr:', errorMsg)
      }
    })

    systemAudioProc.on('close', (code) => {
      console.log('SystemAudioDump process closed with code:', code)
      systemAudioProc = null
    })

    systemAudioProc.on('error', (err) => {
      console.error('SystemAudioDump process error:', err)
      systemAudioProc = null
    })

    // 等待一小段时间确保进程启动
    await new Promise(resolve => setTimeout(resolve, 1000))

    return true
  } catch (error) {
    console.error('Failed to start macOS audio capture:', error)
    return false
  }
}

// 将立体声转换为单声道
function stereoToMono(stereoBuffer: Buffer): Buffer {
  const monoBuffer = Buffer.alloc(stereoBuffer.length / 2)

  for (let i = 0; i < stereoBuffer.length; i += 4) {
    // 读取左右声道的16位样本
    const left = stereoBuffer.readInt16LE(i)
    const right = stereoBuffer.readInt16LE(i + 2)

    // 平均值转换为单声道
    const mono = Math.round((left + right) / 2)

    // 写入单声道缓冲区
    monoBuffer.writeInt16LE(mono, i / 2)
  }

  return monoBuffer
}

function stopSystemAudioCapture() {
  // 停止音频流处理器
  if (audioStreamProcessor) {
    console.log('🎵🛑 Stopping audio stream processor...')
    audioStreamProcessor.stop()
    audioStreamProcessor.destroy()
    audioStreamProcessor = null
  }

  // 停止SystemAudioDump进程
  if (systemAudioProc) {
    console.log('🎤🛑 Stopping SystemAudioDump...')
    systemAudioProc.kill('SIGTERM')
    systemAudioProc = null
  }
}

// 简化的音频处理 - 完全按照 cheatingdaddy 的方式

async function sendAudioToGemini(base64Data: string) {
  if (!geminiSession) return

  try {
    // 直接发送，不使用队列和批处理 - 完全按照 cheatingdaddy 的方式
    await geminiSession.sendRealtimeInput({
      audio: {
        data: base64Data,
        mimeType: 'audio/pcm;rate=24000',
      },
    })
  } catch (error) {
    console.error('Error sending audio to Gemini:', error)
  }
}

// 新增：麦克风音频捕获（占位实现）
async function startMicrophoneCapture(): Promise<boolean> {
  // 目前仅在 macOS 上支持
  if (process.platform !== 'darwin') {
    console.error('🎤❌ Microphone capture not supported on this platform')
    sendToRenderer('session-error', 'Microphone capture not supported on this platform')
    return false
  }

  // 检查麦克风权限
  const micPermission = await checkMicrophonePermission()
  if (!micPermission.granted) {
    console.error('🎤❌ Microphone permission not granted')
    sendToRenderer('session-error', 'Microphone permission not granted')
    return false
  }

  console.log('🎤🚧 Microphone capture stub called - implementation pending')
  // TODO: Implement actual microphone capture logic (e.g., using FFmpeg or node-record-lpcm16)
  return false
}

// 权限检测函数
async function checkScreenRecordingPermission(): Promise<PermissionStatus> {
  try {
    // 检查屏幕录制权限
    const status = systemPreferences.getMediaAccessStatus('screen')

    if (status === 'granted') {
      return {
        granted: true,
        canRequest: false,
        message: '屏幕录制权限已授予'
      }
    } else if (status === 'denied') {
      return {
        granted: false,
        canRequest: false,
        message: '屏幕录制权限被拒绝，请在系统偏好设置中手动授予'
      }
    } else {
      return {
        granted: false,
        canRequest: true,
        message: '需要屏幕录制权限以捕获系统音频'
      }
    }
  } catch (error) {
    console.error('检查屏幕录制权限时出错:', error)
    return {
      granted: false,
      canRequest: false,
      message: '无法检查屏幕录制权限状态'
    }
  }
}

async function checkMicrophonePermission(): Promise<PermissionStatus> {
  try {
    const status = systemPreferences.getMediaAccessStatus('microphone')

    if (status === 'granted') {
      return {
        granted: true,
        canRequest: false,
        message: '麦克风权限已授予'
      }
    } else if (status === 'denied') {
      return {
        granted: false,
        canRequest: false,
        message: '麦克风权限被拒绝，请在系统偏好设置中手动授予'
      }
    } else {
      // 尝试请求权限
      const canRequest = await systemPreferences.askForMediaAccess('microphone')
      return {
        granted: canRequest,
        canRequest: !canRequest,
        message: canRequest ? '麦克风权限已授予' : '需要麦克风权限'
      }
    }
  } catch (error) {
    console.error('检查麦克风权限时出错:', error)
    return {
      granted: false,
      canRequest: false,
      message: '无法检查麦克风权限状态'
    }
  }
}

async function checkApiKeyStatus(): Promise<PermissionStatus> {
  try {
    const apiKey = process.env.VITE_GEMINI_API_KEY

    if (!apiKey || apiKey.trim() === '') {
      return {
        granted: false,
        canRequest: true,
        message: 'Gemini API 密钥未配置'
      }
    }

    if (apiKey.length < 30) {
      return {
        granted: false,
        canRequest: true,
        message: 'API 密钥格式可能不正确'
      }
    }

    return {
      granted: true,
      canRequest: false,
      message: 'API 密钥配置正确'
    }
  } catch (error) {
    console.error('检查API密钥时出错:', error)
    return {
      granted: false,
      canRequest: true,
      message: '无法验证API密钥状态'
    }
  }
}

async function checkAudioDeviceStatus(): Promise<PermissionStatus> {
  try {
    // 首先检查屏幕录制权限，因为SystemAudioDump需要这个权限
    const screenRecordingStatus = systemPreferences.getMediaAccessStatus('screen')

    if (screenRecordingStatus !== 'granted') {
      return {
        granted: false,
        canRequest: true,
        message: 'SystemAudioDump 需要屏幕录制权限才能捕获系统音频'
      }
    }

    // 避免频繁的测试导致窗口闪烁
    return {
      granted: true,
      canRequest: false,
      message: '屏幕录制权限已授予，音频设备应该可以正常工作'
    }
  } catch (error) {
    console.error('检查音频设备时出错:', error)
    return {
      granted: false,
      canRequest: true,
      message: '无法检查音频设备状态'
    }
  }
}

async function getAllPermissionsStatus(): Promise<SystemPermissions> {
  const [screenRecording, microphone, apiKey, audioDevice] = await Promise.all([
    checkScreenRecordingPermission(),
    checkMicrophonePermission(),
    checkApiKeyStatus(),
    checkAudioDeviceStatus()
  ])

  return {
    screenRecording,
    microphone,
    apiKey,
    audioDevice
  }
}

// 打开系统偏好设置
async function openSystemPreferences(pane: string): Promise<boolean> {
  try {
    let command: string

    switch (pane) {
      case 'screen-recording':
        command = 'open "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture"'
        break
      case 'microphone':
        command = 'open "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone"'
        break
      case 'privacy':
        command = 'open "x-apple.systempreferences:com.apple.preference.security"'
        break
      default:
        command = 'open "x-apple.systempreferences:com.apple.preference.security"'
    }

    await execAsync(command)
    return true
  } catch (error) {
    console.error('打开系统偏好设置失败:', error)
    return false
  }
}

// 测试音频捕获
async function testAudioCapture(): Promise<{ success: boolean; message: string; audioData?: number; silencePercentage?: number; recommendation?: string }> {
  try {
    const systemAudioPath = app.isPackaged
      ? join(process.resourcesPath, 'SystemAudioDump')
      : join(__dirname, '../../assets/SystemAudioDump')

    return new Promise((resolve) => {
      const testProc = spawn(systemAudioPath, [], {
        stdio: ['ignore', 'pipe', 'pipe']
      })

      let audioDataSize = 0
      let audioChunks: Buffer[] = []

      testProc.stdout?.on('data', (data: Buffer) => {
        audioDataSize += data.length
        audioChunks.push(data)
      })

      testProc.on('error', (error) => {
        resolve({
          success: false,
          message: `音频捕获测试失败: ${error.message}`,
          recommendation: '请检查SystemAudioDump是否正确安装'
        })
      })

      // 测试5秒以获得更准确的结果
      setTimeout(() => {
        testProc.kill('SIGTERM')

        if (audioDataSize === 0) {
          resolve({
            success: false,
            message: '音频捕获测试失败：没有捕获到任何音频数据',
            audioData: 0,
            silencePercentage: 100,
            recommendation: '请检查屏幕录制权限，并确保有音频正在播放'
          })
          return
        }

        // 分析音频质量
        const combinedBuffer = Buffer.concat(audioChunks)
        const audioStats = analyzeAudioBuffer(combinedBuffer, 'TestAudio')

        if (audioStats.silencePercentage >= 95) {
          resolve({
            success: false,
            message: `音频捕获到数据但全为静音 (${audioStats.silencePercentage.toFixed(1)}% 静音)`,
            audioData: audioDataSize,
            silencePercentage: audioStats.silencePercentage,
            recommendation: '请播放一些音频内容（音乐、视频等）然后重新测试'
          })
        } else if (audioStats.silencePercentage >= 80) {
          resolve({
            success: true,
            message: `音频捕获基本正常，但音量较低 (${audioStats.silencePercentage.toFixed(1)}% 静音)`,
            audioData: audioDataSize,
            silencePercentage: audioStats.silencePercentage,
            recommendation: '建议增加系统音量或播放更响亮的音频内容'
          })
        } else {
          resolve({
            success: true,
            message: `音频捕获正常！捕获了 ${audioDataSize} 字节数据 (${audioStats.silencePercentage.toFixed(1)}% 静音)`,
            audioData: audioDataSize,
            silencePercentage: audioStats.silencePercentage,
            recommendation: '音频捕获工作正常，可以使用Live Interview模式'
          })
        }
      }, 5000)
    })
  } catch (error) {
    return {
      success: false,
      message: `音频捕获测试出错: ${error.message}`,
      recommendation: '请检查SystemAudioDump是否正确安装和配置'
    }
  }
}
