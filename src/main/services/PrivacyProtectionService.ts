import { BrowserWindow } from 'electron';
import { StealthModeService, StealthLevel } from './StealthModeService';
import { AntiDetectionService } from './AntiDetectionService';
import { PerformanceOptimizer } from './PerformanceOptimizer';
import { SecurityValidator } from './SecurityValidator';
import { AdvancedWindowHider } from './AdvancedWindowHider';
import { GlobalEventProtector } from './GlobalEventProtector';

export interface PrivacyConfig {
  enableScreenCaptureProtection?: boolean;
  enableVisibilityProtection?: boolean;
  enableWindowInfoProtection?: boolean;
  enableAPIProtection?: boolean;
  stealthMode?: boolean;
  enableAdvancedStealth?: boolean;
  enableProcessHiding?: boolean;
  enableMemoryProtection?: boolean;
  enableGlobalEventProtection?: boolean;
  enableKeyboardProtection?: boolean;
  enableMouseProtection?: boolean;
}

/**
 * 隐私保护服务
 * 提供反检测、屏幕保护等功能
 */
export class PrivacyProtectionService {
  private config: PrivacyConfig;
  private stealthService: StealthModeService | null = null;
  private antiDetectionService: AntiDetectionService | null = null;
  private performanceOptimizer: PerformanceOptimizer | null = null;
  private securityValidator: SecurityValidator | null = null;
  private advancedWindowHider: AdvancedWindowHider | null = null;
  private globalEventProtector: GlobalEventProtector | null = null;

  constructor(config: PrivacyConfig = {}) {
    this.config = {
      enableScreenCaptureProtection: true,
      enableVisibilityProtection: true,
      enableWindowInfoProtection: true,
      enableAPIProtection: true,
      stealthMode: true,
      enableAdvancedStealth: true,
      enableProcessHiding: true,
      enableMemoryProtection: true,
      enableGlobalEventProtection: true,
      enableKeyboardProtection: true,
      enableMouseProtection: true,
      ...config
    };

    // 如果启用高级隐身，初始化隐身服务
    if (this.config.enableAdvancedStealth) {
      this.stealthService = new StealthModeService({
        enableGlobalShortcuts: true,
        defaultStealthLevel: StealthLevel.ENHANCED,
        enableWindowPositionControl: true,
        enableTransparencyControl: true,
        enableMousePassthrough: false
      });

      // 初始化反检测服务
      this.antiDetectionService = new AntiDetectionService({
        enableProcessMasking: this.config.enableProcessHiding,
        enableNetworkObfuscation: true,
        enableMemoryProtection: this.config.enableMemoryProtection,
        enableFileSystemHiding: true,
        enableAPIHooking: true,
        enableMousePassthrough: false
      });

      // 初始化性能优化器
      this.performanceOptimizer = new PerformanceOptimizer({
        enableMemoryOptimization: true,
        enableCPUOptimization: true,
        enableNetworkOptimization: true,
        enableStealthOptimization: true,
        maxMemoryUsage: 512,
        maxCPUUsage: 30,
        optimizationInterval: 5000
      });

      // 初始化安全验证器
      this.securityValidator = new SecurityValidator();

      // 初始化高级窗口隐藏器
      this.advancedWindowHider = new AdvancedWindowHider({
        enableSystemLevelHiding: true,
        enableProcessMasking: true,
        enableAPIInterception: true,
        enableTaskSwitcherHiding: true
      });

      // 初始化全局事件保护器
      if (this.config.enableGlobalEventProtection) {
        this.globalEventProtector = new GlobalEventProtector({
          enableKeyboardProtection: this.config.enableKeyboardProtection,
          enableMouseProtection: this.config.enableMouseProtection,
          enableGlobalMouseListener: true,
          enableDebugLog: true
        });
      }
    }
  }

  /**
   * 🚀 初始化隐私保护服务
   */
  async initialize(): Promise<void> {
    if (this.stealthService) {
      await this.stealthService.initialize();
      console.log('🔒 Advanced stealth service initialized');
    }

    if (this.antiDetectionService) {
      await this.antiDetectionService.initialize();
      console.log('🕵️ Anti-detection service initialized');
    }

    if (this.performanceOptimizer) {
      await this.performanceOptimizer.initialize();
      console.log('⚡ Performance optimizer initialized');
    }

    if (this.securityValidator) {
      await this.securityValidator.initialize();
      console.log('🔒 Security validator initialized');
    }

    if (this.advancedWindowHider) {
      await this.advancedWindowHider.initialize();
      console.log('🕳️ Advanced window hider initialized');
    }

    if (this.globalEventProtector) {
      await this.globalEventProtector.initialize();
      console.log('🛡️ Global event protector initialized');
    }
  }

  /**
   * 🔒 为窗口应用隐私保护设置
   */
  applyWindowProtection(window: BrowserWindow, isFloating = false): void {
    // 应用基础隐私保护
    if (process.platform === 'darwin') {
      this.applyMacOSProtection(window, isFloating);
    } else if (process.platform === 'win32') {
      this.applyWindowsProtection(window, isFloating);
    } else if (process.platform === 'linux') {
      this.applyLinuxProtection(window, isFloating);
    }

    // 应用高级隐身保护
    if (this.stealthService) {
      if (isFloating) {
        this.stealthService.setFloatingWindow(window);
      } else {
        this.stealthService.setMainWindow(window);
      }
    }

    // 应用高级防检测保护
    if (this.config.enableAdvancedStealth) {
      this.applyAdvancedStealthProtection(window, isFloating);
    }

    // 应用反检测保护
    if (this.antiDetectionService) {
      this.antiDetectionService.applyWindowAntiDetection(window);
    }
  }

  /**
   * 🔒 注入反检测脚本
   */
  injectAntiDetectionScripts(window: BrowserWindow, isFloating = false): void {
    const scripts = this.generateAntiDetectionScripts(isFloating);
    window.webContents.executeJavaScript(scripts).catch((error) => {
      console.log('🔒 Failed to inject anti-detection scripts:', error);
    });
  }

  /**
   * 🔒 macOS 特定保护
   */
  private applyMacOSProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      // 屏幕捕获保护
      if (this.config.enableScreenCaptureProtection) {
        (window as any).setContentProtection?.(true);
        (window as any).setPrivacySensitive?.(true);
      }

      // 浮动窗口特殊设置
      if (isFloating && this.config.stealthMode) {
        (window as any).setLevel?.('screen-saver');
        (window as any).setWindowButtonVisibility?.(false);
        (window as any).setVisibleOnAllWorkspaces?.(false);
      }
    } catch (error) {
      console.log('🔒 macOS protection features not available:', error);
    }
  }

  /**
   * 🔒 Windows 特定保护
   */
  private applyWindowsProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      // Windows 特定的保护措施
      if (this.config.enableScreenCaptureProtection) {
        // 设置窗口为受保护内容
        window.setContentProtection(true);
      }

      if (isFloating && this.config.stealthMode) {
        // 设置为工具窗口，减少在任务栏的可见性
        window.setSkipTaskbar(true);
      }
    } catch (error) {
      console.log('🔒 Windows protection features not available:', error);
    }
  }

  /**
   * 🔒 Linux 特定保护
   */
  private applyLinuxProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      if (isFloating && this.config.stealthMode) {
        window.setSkipTaskbar(true);
      }
    } catch (error) {
      console.log('🔒 Linux protection features not available:', error);
    }
  }

  /**
   * 🔒 生成反检测脚本
   */
  private generateAntiDetectionScripts(isFloating: boolean): string {
    let scripts = `
      console.log('🔒 Initializing privacy protection...');
    `;

    // 屏幕捕获保护
    if (this.config.enableScreenCaptureProtection) {
      scripts += `
        // 防止屏幕录制检测
        if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
          navigator.mediaDevices.getDisplayMedia = function() {
            console.log('🔒 Screen capture attempt blocked');
            return Promise.reject(new DOMException('Permission denied', 'NotAllowedError'));
          };
        }
        
        // 防止getUserMedia被滥用
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
          navigator.mediaDevices.getUserMedia = function(constraints) {
            if (constraints && constraints.video && constraints.video.mandatory) {
              console.log('🔒 Suspicious getUserMedia call blocked');
              return Promise.reject(new DOMException('Permission denied', 'NotAllowedError'));
            }
            return originalGetUserMedia.call(this, constraints);
          };
        }
      `;
    }

    // 窗口信息保护
    if (this.config.enableWindowInfoProtection) {
      scripts += `
        // 防止窗口位置检测
        Object.defineProperty(window, 'screenX', {
          get: function() { return 0; },
          configurable: false
        });
        
        Object.defineProperty(window, 'screenY', {
          get: function() { return 0; },
          configurable: false
        });
        
        Object.defineProperty(window, 'screenLeft', {
          get: function() { return 0; },
          configurable: false
        });
        
        Object.defineProperty(window, 'screenTop', {
          get: function() { return 0; },
          configurable: false
        });
        
        // 防止窗口大小检测
        Object.defineProperty(window, 'outerWidth', {
          get: function() { return screen.width; },
          configurable: false
        });
        
        Object.defineProperty(window, 'outerHeight', {
          get: function() { return screen.height; },
          configurable: false
        });
      `;
    }

    // API保护
    if (this.config.enableAPIProtection) {
      scripts += `
        // 防止焦点检测
        const originalFocus = window.focus;
        window.focus = function() {
          if (${isFloating}) {
            // 浮动窗口不获取焦点
            return;
          }
          return originalFocus.call(this);
        };
        
        const originalBlur = window.blur;
        window.blur = function() {
          if (${isFloating}) {
            // 浮动窗口不失去焦点
            return;
          }
          return originalBlur.call(this);
        };
        
        // 防止窗口枚举
        if (window.external && window.external.AddSearchProvider) {
          window.external.AddSearchProvider = function() {
            console.log('🔒 Search provider enumeration blocked');
          };
        }
      `;
    }

    // 浮动窗口特殊保护
    if (this.config.stealthMode && isFloating) {
      scripts += `
        // 浮动窗口特殊保护
        
        // 防止窗口检测
        Object.defineProperty(window, 'name', {
          get: function() { return ''; },
          set: function() { return; },
          configurable: false
        });
        
        // 防止opener检测
        Object.defineProperty(window, 'opener', {
          get: function() { return null; },
          set: function() { return; },
          configurable: false
        });
        
        // 防止parent检测
        Object.defineProperty(window, 'parent', {
          get: function() { return window; },
          configurable: false
        });
        
        Object.defineProperty(window, 'top', {
          get: function() { return window; },
          configurable: false
        });
        
        // 防止iframe检测
        Object.defineProperty(window, 'frameElement', {
          get: function() { return null; },
          configurable: false
        });
        
        // 防止窗口历史检测
        Object.defineProperty(window.history, 'length', {
          get: function() { return 1; },
          configurable: false
        });
      `;
    }

    scripts += `
      console.log('🔒 Privacy protection initialized successfully');
    `;

    return scripts;
  }

  /**
   * 🥷 应用高级隐身保护
   */
  private applyAdvancedStealthProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      // 进程隐藏保护
      if (this.config.enableProcessHiding) {
        this.applyProcessHiding(window);
      }

      // 内存保护
      if (this.config.enableMemoryProtection) {
        this.applyMemoryProtection(window);
      }

      // 窗口检测防护
      this.applyWindowDetectionProtection(window, isFloating);

      console.log('🥷 Advanced stealth protection applied');
    } catch (error) {
      console.log('🥷 Advanced stealth protection failed:', error);
    }
  }

  /**
   * 🔒 进程隐藏保护
   */
  private applyProcessHiding(window: BrowserWindow): void {
    try {
      // 设置窗口为不可枚举
      window.setSkipTaskbar(true);

      // 平台特定的进程隐藏
      if (process.platform === 'win32') {
        // Windows: 设置窗口为工具窗口
        // @ts-ignore - Windows specific API
        window.setParentWindow?.(null);
      } else if (process.platform === 'darwin') {
        // macOS: 设置为后台应用
        // @ts-ignore - macOS specific API
        window.setVisibleOnAllWorkspaces?.(false);
      }
    } catch (error) {
      console.log('🔒 Process hiding failed:', error);
    }
  }

  /**
   * 🧠 内存保护
   */
  private applyMemoryProtection(window: BrowserWindow): void {
    try {
      // 防止内存转储
      if (process.platform === 'win32') {
        // Windows: 设置进程为关键进程（需要管理员权限）
        console.log('🧠 Memory protection enabled (Windows)');
      } else if (process.platform === 'darwin') {
        // macOS: 设置内存保护标志
        // @ts-ignore - macOS specific API
        window.setContentProtection?.(true);
        console.log('🧠 Memory protection enabled (macOS)');
      }
    } catch (error) {
      console.log('🧠 Memory protection failed:', error);
    }
  }

  /**
   * 🕵️ 窗口检测防护
   */
  private applyWindowDetectionProtection(window: BrowserWindow, isFloating: boolean): void {
    try {
      // 随机化窗口标题
      const randomTitle = this.generateRandomTitle();
      window.setTitle(randomTitle);

      // 防止窗口枚举
      window.setSkipTaskbar(true);

      // 设置窗口层级
      if (isFloating) {
        window.setAlwaysOnTop(true);
        // 设置为屏幕保护程序级别（最高级别）
        if (process.platform === 'darwin') {
          // @ts-ignore - macOS specific API
          window.setLevel?.('screen-saver');
        }
      }

      console.log('🕵️ Window detection protection applied');
    } catch (error) {
      console.log('🕵️ Window detection protection failed:', error);
    }
  }

  /**
   * 🎲 生成随机窗口标题
   */
  private generateRandomTitle(): string {
    const titles = [
      'System Process',
      'Background Task',
      'Service Host',
      'System Monitor',
      'Task Manager',
      'Windows Security',
      'System Update',
      'Network Service'
    ];
    return titles[Math.floor(Math.random() * titles.length)];
  }

  /**
   * 📊 获取隐身状态
   */
  getStealthStatus() {
    if (this.stealthService) {
      return this.stealthService.getStealthStatus();
    }
    return null;
  }

  /**
   * 🎮 获取隐身服务实例
   */
  getStealthService(): StealthModeService | null {
    return this.stealthService;
  }

  /**
   * 🕵️ 获取反检测服务实例
   */
  getAntiDetectionService(): AntiDetectionService | null {
    return this.antiDetectionService;
  }

  /**
   * 🕳️ 获取高级窗口隐藏器实例
   */
  getAdvancedWindowHider(): AdvancedWindowHider | null {
    return this.advancedWindowHider;
  }

  /**
   * 🛡️ 获取全局事件保护器实例
   */
  getGlobalEventProtector(): GlobalEventProtector | null {
    return this.globalEventProtector;
  }

  /**
   * 🖱️ 切换鼠标穿透模式
   */
  toggleMousePassthrough(window: BrowserWindow): boolean {
    if (this.antiDetectionService) {
      return this.antiDetectionService.toggleMousePassthrough(window);
    }
    return false;
  }

  /**
   * 📊 获取反检测状态
   */
  getAntiDetectionStatus() {
    if (this.antiDetectionService) {
      return this.antiDetectionService.getAntiDetectionStatus();
    }
    return null;
  }

  /**
   * 🛡️ 获取全局事件保护状态
   */
  getGlobalEventProtectionStatus() {
    if (this.globalEventProtector) {
      return this.globalEventProtector.getProtectionStatus();
    }
    return null;
  }

  /**
   * ⚡ 获取性能报告
   */
  getPerformanceReport() {
    if (this.performanceOptimizer) {
      return this.performanceOptimizer.getPerformanceReport();
    }
    return null;
  }

  /**
   * 💡 获取优化建议
   */
  getOptimizationSuggestions(): string[] {
    if (this.performanceOptimizer) {
      return this.performanceOptimizer.getOptimizationSuggestions();
    }
    return [];
  }

  /**
   * 🔒 获取安全报告
   */
  getSecurityReport() {
    if (this.securityValidator) {
      return this.securityValidator.getSecurityReport();
    }
    return null;
  }

  /**
   * 🔧 获取兼容性信息
   */
  getCompatibilityInfo() {
    if (this.securityValidator) {
      return this.securityValidator.getCompatibilityInfo();
    }
    return null;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<PrivacyConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): PrivacyConfig {
    return { ...this.config };
  }

  /**
   * 🧹 清理资源
   */
  destroy(): void {
    if (this.stealthService) {
      this.stealthService.destroy();
    }
    if (this.antiDetectionService) {
      this.antiDetectionService.destroy();
    }
    if (this.performanceOptimizer) {
      this.performanceOptimizer.destroy();
    }
    if (this.securityValidator) {
      this.securityValidator.destroy();
    }
    if (this.advancedWindowHider) {
      this.advancedWindowHider.destroy();
    }
    if (this.globalEventProtector) {
      this.globalEventProtector.destroy();
    }
  }
}
