import { promises as fs } from 'fs';

/**
 * 🤖 智谱AI Vision 分析服务
 * 使用智谱AI的GLM-4V视觉模型直接分析图片
 */

export interface ZhipuVisionResult {
  success: boolean;
  category?: string;
  quickAnswer?: string;
  keyPoints?: string[];
  confidence?: number;
  response?: string;
  detailedAnalysis?: string;
  method: string;
  error?: string;
}

export class ZhipuVisionService {
  private apiKey: string;
  private requestTimeout = 30000; // 30秒超时

  constructor(apiKey: string = 'e6e4c82752a6463bb7f3b0991a757d1e.Ffm4xAFzaeaQjUj0') {
    this.apiKey = apiKey;
  }

  /**
   * 🖼️ 分析截图图片
   */
  async analyzeScreenshot(imagePath: string): Promise<ZhipuVisionResult> {
    try {
      console.log('🤖 Starting Zhipu AI vision analysis...');
      console.log('📁 Image path:', imagePath);

      // 1. 检查图片文件
      if (!await this.checkImageFile(imagePath)) {
        return {
          success: false,
          error: 'Image file not found or invalid',
          method: 'Zhipu AI GLM-4V Vision Analysis'
        };
      }

      // 2. 转换图片为base64
      const imageBase64 = await this.convertImageToBase64(imagePath);
      if (!imageBase64) {
        return {
          success: false,
          error: 'Failed to convert image to base64',
          method: 'Zhipu AI GLM-4V Vision Analysis'
        };
      }

      // 3. 调用智谱AI Vision API
      const result = await this.callZhipuVisionAPI(imageBase64);
      if (result.success) {
        console.log('🤖✅ Zhipu AI vision analysis completed successfully');
        return {
          ...result,
          method: 'Zhipu AI GLM-4V Vision Analysis'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Zhipu AI API call failed',
          method: 'Zhipu AI GLM-4V Vision Analysis'
        };
      }

    } catch (error) {
      console.error('🤖❌ Zhipu AI vision analysis failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        method: 'Zhipu AI GLM-4V Vision Analysis'
      };
    }
  }

  /**
   * 🔍 检查图片文件
   */
  private async checkImageFile(imagePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(imagePath);
      return stats.isFile() && stats.size > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 🔄 转换图片为base64
   */
  private async convertImageToBase64(imagePath: string): Promise<string | null> {
    try {
      const imageBuffer = await fs.readFile(imagePath);
      
      // 检查图片大小
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (imageBuffer.length > maxSize) {
        console.warn('🤖⚠️ Image too large for Zhipu AI API');
      }

      const base64 = imageBuffer.toString('base64');
      console.log(`🔄 Image converted to base64, size: ${Math.round(base64.length / 1024)}KB`);
      
      return base64;
    } catch (error) {
      console.error('🔄❌ Failed to convert image to base64:', error);
      return null;
    }
  }

  /**
   * 🤖 调用智谱AI Vision API
   */
  private async callZhipuVisionAPI(imageBase64: string): Promise<ZhipuVisionResult> {
    try {
      const prompt = `你是一个专业的面试助手。请仔细分析这张截图中的具体面试题目，根据实际内容提供针对性的回答。

重要：这是技术岗面试准备，如果是编程相关题目，必须提供完整的代码实现！

请提供：
1. 题目类型分类（算法题、系统设计、技术概念、行为题等）
2. 核心解题思路（3-5个要点）
3. 简洁的口头回答（30-50字，适合面试时直接说出）
4. 详细解析，必须包含完整的Java代码实现（对于编程题）

请严格按照以下JSON格式返回，根据实际题目内容填写：
{
  "category": "算法题",
  "quickAnswer": "根据实际题目提供简洁回答",
  "keyPoints": ["关键点1", "关键点2", "关键点3"],
  "response": "详细的解题思路说明，包含完整的Java代码实现：\\n\\n\`\`\`java\\npublic class Solution {\\n    public int solve(参数) {\\n        // 具体实现代码\\n        return result;\\n    }\\n}\\n\`\`\`\\n\\n时间复杂度：O(n)\\n空间复杂度：O(1)",
  "confidence": 0.9
}

重要：
1. 请根据截图中的实际内容进行分析
2. 对于编程题，response字段必须包含完整的Java代码实现
3. 代码要用代码块包围，确保格式正确`;

      const requestBody = {
        model: 'glm-4v-plus', // 使用智谱AI的视觉模型
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/png;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: 1500,
        temperature: 0.3
      };

      console.log(`🤖 Calling Zhipu AI Vision API with GLM-4V-Plus`);
      console.log(`🔑 Using API key: ${this.apiKey.substring(0, 10)}...`);

      const response = await fetch('https://open.bigmodel.cn/api/paas/v4/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.requestTimeout)
      });

      console.log(`🤖 Zhipu AI API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`🤖❌ Zhipu AI API Error Response: ${errorText}`);
        throw new Error(`Zhipu AI API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      const content = result.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('No content in Zhipu AI API response');
      }

      console.log('🤖 Zhipu AI API response content:', content.substring(0, 200) + '...');

      // 解析JSON响应
      try {
        // 清理可能的JSON标记和格式问题
        let cleanContent = content.trim();

        // 移除```json和```标记
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        console.log('🤖 Cleaned content for JSON parsing:', cleanContent.substring(0, 300) + '...');

        const analysis = JSON.parse(cleanContent);

        // 处理response字段中的转义字符
        let processedResponse = analysis.response || content;
        if (typeof processedResponse === 'string') {
          // 处理转义的换行符和反引号
          processedResponse = processedResponse
            .replace(/\\n/g, '\n')
            .replace(/\\`/g, '`')
            .replace(/\\\\/g, '\\');
        }

        return {
          success: true,
          category: analysis.category || 'general',
          quickAnswer: analysis.quickAnswer || '需要更多信息来回答',
          keyPoints: analysis.keyPoints || ['请提供更清晰的问题'],
          confidence: analysis.confidence || 0.7,
          response: processedResponse,
          detailedAnalysis: processedResponse
        };
      } catch (parseError) {
        console.error('🤖❌ JSON parsing failed:', parseError);
        console.log('🤖 Raw content:', content);

        // 如果JSON解析失败，尝试直接使用内容
        // 处理可能的转义字符
        let processedContent = content
          .replace(/\\n/g, '\n')
          .replace(/\\`/g, '`')
          .replace(/\\\\/g, '\\');

        return {
          success: true,
          category: 'general',
          quickAnswer: processedContent.substring(0, 100),
          keyPoints: ['智谱AI分析结果'],
          confidence: 0.6,
          response: processedContent,
          detailedAnalysis: processedContent
        };
      }

    } catch (error) {
      console.error('🤖❌ Zhipu AI Vision API call failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 📊 获取服务状态
   */
  async getServiceStatus(): Promise<{
    available: boolean;
    model: string;
    apiKey: string;
    lastError?: string;
  }> {
    return {
      available: !!this.apiKey,
      model: 'glm-4v-plus',
      apiKey: this.apiKey.substring(0, 15) + '...'
    };
  }
}
