import { EventEmitter } from 'events';
import { ServiceConfigManager } from './ServiceConfigManager';
import { ServiceAdapter } from './ServiceAdapter';
import {
  ServiceMode,
  TranscriptionProvider,
  AIProvider,
  ServiceEvent,
  ServiceConfig
} from './ServiceTypes';

/**
 * 统一服务管理器
 * 管理所有AI服务的生命周期和配置
 */
export class ServiceManager extends EventEmitter {
  private static instance: ServiceManager | null = null;
  private currentAdapter: ServiceAdapter | null = null;
  private isInitialized = false;
  private currentConfig: ServiceConfig | null = null;
  private isSessionActive = false;

  constructor() {
    super();
    console.log('ServiceManager created');
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  /**
   * 初始化服务管理器
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🔧 ServiceManager: Initializing (config only, no API connections)...');

      // 初始化配置管理器
      const configManager = ServiceConfigManager.getInstance();
      await configManager.initialize();

      this.currentConfig = configManager.getConfig();

      // 只准备服务适配器，不建立连接
      await this.prepareServiceAdapter(this.currentConfig);

      this.isInitialized = true;
      console.log('🔧✅ ServiceManager initialized successfully (ready for on-demand connections)');

      return true;
    } catch (error) {
      console.error('🔧❌ Failed to initialize ServiceManager:', error);
      return false;
    }
  }

  /**
   * 准备服务适配器（不建立连接）
   */
  private async prepareServiceAdapter(config: ServiceConfig): Promise<boolean> {
    try {
      console.log('🔧 ServiceManager: Preparing service adapter for mode:', config.mode);

      // 清理现有适配器
      if (this.currentAdapter) {
        this.currentAdapter.removeAllListeners();
        await this.currentAdapter.stopSession();
        this.currentAdapter = null;
      }

      // 创建新适配器但不初始化连接
      this.currentAdapter = await this.createAdapter(config);
      this.setupAdapterEvents(this.currentAdapter);

      console.log('🔧✅ ServiceManager: Service adapter prepared successfully');
      return true;
    } catch (error) {
      console.error('🔧❌ ServiceManager: Failed to prepare service adapter:', error);
      return false;
    }
  }

  /**
   * 切换到指定服务
   */
  async switchToService(config: ServiceConfig): Promise<boolean> {
    try {
      console.log('Switching to service:', config.mode);
      
      // 检查是否需要切换
      if (this.currentAdapter && this.currentConfig && this.currentConfig.mode === config.mode) {
        if (config.mode === ServiceMode.SEPARATED && this.currentConfig.separated && config.separated) {
          const currentTransProvider = this.currentConfig.separated.transcription?.provider;
          const newTransProvider = config.separated.transcription?.provider;
          const currentAIProvider = this.currentConfig.separated.ai?.provider;
          const newAIProvider = config.separated.ai?.provider;
          
          if (currentTransProvider === newTransProvider && currentAIProvider === newAIProvider) {
            console.log('Service already using the same providers, skipping switch');
            return true;
          }
        } else if (config.mode === ServiceMode.GEMINI_LIVE) {
          console.log('Service already using gemini-live, skipping switch');
          return true;
        }
      }

      // 清理当前适配器
      if (this.currentAdapter) {
        await this.currentAdapter.stopSession();
        await this.currentAdapter.disconnect();
        this.currentAdapter.removeAllListeners();
        this.currentAdapter = null;
      }

      // 准备新的服务适配器（不建立连接）
      const success = await this.prepareServiceAdapter(config);

      if (success) {
        this.currentConfig = config;

        // 保存配置
        const configManager = ServiceConfigManager.getInstance();
        await configManager.updateConfig(config);

        console.log('🔧✅ Service switched successfully to:', config.mode);
        this.emit('service-switched', config.mode);
        return true;
      } else {
        console.error('🔧❌ Failed to prepare new service adapter');
        return false;
      }
    } catch (error) {
      console.error('Failed to switch service:', error);
      return false;
    }
  }

  /**
   * 启动当前服务会话（这里才真正连接API）
   */
  async startSession(): Promise<boolean> {
    if (!this.isInitialized) {
      console.error('🔧❌ ServiceManager not initialized');
      return false;
    }

    if (!this.currentAdapter) {
      console.error('🔧❌ No service adapter available');
      return false;
    }

    // 防止重复启动
    if (this.isSessionActive) {
      console.log('🔧 ServiceManager: Session already active, skipping...');
      return true;
    }

    try {
      console.log('🔧 ServiceManager: Starting session with current config...');
      this.isSessionActive = true;

      // 首先初始化适配器（建立API连接）
      const initSuccess = await this.currentAdapter.initialize(this.currentConfig);
      if (!initSuccess) {
        console.error('🔧❌ Failed to initialize adapter for session');
        this.isSessionActive = false;
        return false;
      }

      // 然后启动会话
      const sessionSuccess = await this.currentAdapter.startSession();
      if (sessionSuccess) {
        console.log('🔧✅ Service session started successfully');
        this.emit('session-started');
        return true;
      } else {
        console.error('🔧❌ Failed to start service session');
        this.isSessionActive = false;
        return false;
      }
    } catch (error) {
      console.error('🔧❌ Failed to start service session:', error);
      this.isSessionActive = false;
      return false;
    }
  }

  /**
   * 停止当前服务会话（断开所有API连接）
   */
  async stopSession(): Promise<void> {
    if (this.currentAdapter && this.isSessionActive) {
      try {
        console.log('🔧 ServiceManager: Stopping session and disconnecting APIs...');
        await this.currentAdapter.stopSession();
        this.isSessionActive = false;
        console.log('🔧✅ Service session stopped and APIs disconnected');
        this.emit('session-stopped');
      } catch (error) {
        console.error('🔧❌ Failed to stop service session:', error);
        this.isSessionActive = false;
      }
    } else {
      console.log('🔧 ServiceManager: No active session to stop');
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (this.currentAdapter) {
      await this.currentAdapter.sendAudio(audioData);
    }
  }

  /**
   * 重新连接当前服务
   */
  async reconnect(): Promise<boolean> {
    if (!this.currentAdapter) {
      console.error('No service adapter available for reconnection');
      return false;
    }

    try {
      const success = await this.currentAdapter.reconnect();
      if (success) {
        console.log('Service reconnected successfully');
        this.emit('service-reconnected');
      }
      return success;
    } catch (error) {
      console.error('Failed to reconnect service:', error);
      return false;
    }
  }

  /**
   * 断开当前服务
   */
  async disconnect(): Promise<void> {
    if (this.currentAdapter) {
      try {
        await this.currentAdapter.disconnect();
        console.log('Service disconnected');
        this.emit('service-disconnected');
      } catch (error) {
        console.error('Failed to disconnect service:', error);
      }
    }
  }

  /**
   * 获取当前服务状态
   */
  getServiceStatus(): any {
    return this.currentAdapter ? this.currentAdapter.getStatus() : null;
  }

  /**
   * 获取当前配置
   */
  getCurrentConfig(): ServiceConfig | null {
    return this.currentConfig ? { ...this.currentConfig } : null;
  }

  /**
   * 重新加载配置
   */
  async reloadConfig(): Promise<ServiceConfig | null> {
    try {
      const configManager = ServiceConfigManager.getInstance();
      await configManager.initialize(); // 重新初始化以加载最新配置
      this.currentConfig = configManager.getConfig();
      console.log('🔧 ServiceManager: Config reloaded from file system');
      return this.currentConfig;
    } catch (error) {
      console.error('🔧❌ ServiceManager: Failed to reload config:', error);
      return null;
    }
  }

  /**
   * 获取当前适配器
   */
  getCurrentAdapter(): ServiceAdapter | null {
    return this.currentAdapter;
  }

  /**
   * 获取当前服务模式
   */
  getCurrentMode(): ServiceMode | null {
    return this.currentConfig?.mode || null;
  }

  /**
   * 检查服务是否就绪
   */
  isServiceReady(): boolean {
    return this.isInitialized && this.currentAdapter?.isReady() === true;
  }

  /**
   * 检查会话是否活跃
   */
  isSessionReady(): boolean {
    return this.isSessionActive && this.isServiceReady();
  }

  /**
   * 获取当前服务的音频要求
   */
  getAudioRequirements(): any {
    if (this.currentAdapter && typeof this.currentAdapter.getAudioRequirements === 'function') {
      return this.currentAdapter.getAudioRequirements();
    }
    return null;
  }

  /**
   * 更新服务配置
   */
  async updateConfig(config: Partial<ServiceConfig>): Promise<boolean> {
    console.log('🔧 ServiceManager: Updating config with:', JSON.stringify(config, null, 2));
    
    if (!this.currentConfig) {
      console.error('No current config to update');
      return false;
    }

    let finalConfig: ServiceConfig;
    
    if (config.mode && config.separated && config.mode === ServiceMode.SEPARATED) {
      finalConfig = config as ServiceConfig;
      console.log('🔧 ServiceManager: Using complete separated config');
    } else if (config.mode && config.geminiLive && config.mode === ServiceMode.GEMINI_LIVE) {
      finalConfig = config as ServiceConfig;
      console.log('🔧 ServiceManager: Using complete gemini-live config');
    } else {
      finalConfig = { ...this.currentConfig, ...config };
      console.log('🔧 ServiceManager: Merging partial config');
    }

    console.log('🔧 ServiceManager: Final config:', JSON.stringify(finalConfig, null, 2));

    // 如果模式改变，需要切换服务
    if (config.mode && config.mode !== this.currentConfig.mode) {
      console.log('🔧 ServiceManager: Mode changed, switching service');
      return await this.switchToService(finalConfig);
    }

    try {
      // 首先保存配置到文件系统
      const configManager = ServiceConfigManager.getInstance();
      await configManager.updateConfig(finalConfig);
      console.log('🔧 ServiceManager: Config saved to file system');

      // 更新内存中的配置
      this.currentConfig = finalConfig;
      console.log('🔧 ServiceManager: Memory config updated');

      // 如果有适配器，更新适配器配置
      if (this.currentAdapter) {
        const adapterSuccess = await this.currentAdapter.updateConfig(finalConfig);
        if (!adapterSuccess) {
          console.warn('🔧 ServiceManager: Adapter config update failed, but file config saved');
        }
      }

      // 发送配置更新事件
      this.emit('config-updated', finalConfig);
      console.log('🔧 ServiceManager: Config update event emitted');

      return true;
    } catch (error) {
      console.error('🔧❌ ServiceManager: Failed to update service config:', error);
      return false;
    }
  }

  /**
   * 创建服务适配器
   */
  private async createAdapter(config: ServiceConfig): Promise<ServiceAdapter> {
    switch (config.mode) {
      case ServiceMode.GEMINI_LIVE:
        const { GeminiLiveAdapter } = await import('./GeminiLiveAdapter');
        return new GeminiLiveAdapter(config);
      case ServiceMode.SEPARATED:
        const { SeparatedAdapter } = await import('./SeparatedAdapter');
        return new SeparatedAdapter(config);
      default:
        throw new Error(`Unsupported service mode: ${config.mode}`);
    }
  }

  /**
   * 设置适配器事件监听
   */
  private setupAdapterEvents(adapter: ServiceAdapter): void {
    adapter.on('service-event', (event: any) => {
      console.log('Service event:', event.event, event.data);
      this.emit('service-event', event);
      
      switch (event.event) {
        case ServiceEvent.STATUS_CHANGED:
          this.emit('status-changed', event.data);
          break;
        case ServiceEvent.TRANSCRIPTION_RECEIVED:
          this.emit('transcription-received', event.data);
          break;
        case ServiceEvent.AI_RESPONSE_RECEIVED:
          this.emit('ai-response-received', event.data);
          break;
        case ServiceEvent.ERROR_OCCURRED:
          this.emit('error-occurred', event.data);
          break;
      }
    });

    adapter.on('error', (error: any) => {
      console.error('Service adapter error:', error);
      this.emit('adapter-error', error);
    });
  }

  /**
   * 更新转录语言
   */
  async updateTranscriptionLanguage(language: string): Promise<void> {
    console.log('🌍 ServiceManager: Updating transcription language to:', language);

    if (!this.currentAdapter) {
      throw new Error('No active service adapter');
    }

    // 更新当前配置
    if (this.currentConfig) {
      this.currentConfig.transcription.language = language;
      console.log('🌍 Updated config language to:', language);
    }

    // 通知适配器更新语言
    if (typeof this.currentAdapter.updateTranscriptionLanguage === 'function') {
      await this.currentAdapter.updateTranscriptionLanguage(language);
    } else {
      console.warn('Current adapter does not support language update');
    }
  }

  /**
   * 更新自定义提示词
   */
  async updateCustomPrompt(customPrompt: string): Promise<void> {
    console.log('🎯 ServiceManager: Updating custom prompt');

    if (!this.currentAdapter) {
      throw new Error('No active service adapter');
    }

    // 更新当前配置
    if (this.currentConfig) {
      if (this.currentConfig.mode === ServiceMode.GEMINI_LIVE && this.currentConfig.geminiLive) {
        this.currentConfig.geminiLive.customPrompt = customPrompt;
      } else if (this.currentConfig.mode === ServiceMode.SEPARATED && this.currentConfig.separated) {
        // 对于分离模式，我们可以在AI配置中添加customPrompt字段
        if (!this.currentConfig.separated.ai.config.customPrompt) {
          this.currentConfig.separated.ai.config.customPrompt = customPrompt;
        } else {
          this.currentConfig.separated.ai.config.customPrompt = customPrompt;
        }
      }
      console.log('🎯 Updated config with custom prompt');
    }

    // 通知适配器更新提示词
    if (typeof this.currentAdapter.updateCustomPrompt === 'function') {
      await this.currentAdapter.updateCustomPrompt(customPrompt);
    } else {
      console.warn('Current adapter does not support custom prompt update');
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('Cleaning up ServiceManager...');

    if (this.currentAdapter) {
      await this.currentAdapter.stopSession();
      await this.currentAdapter.disconnect();
      this.currentAdapter.removeAllListeners();
      this.currentAdapter = null;
    }

    this.removeAllListeners();
    this.isInitialized = false;

    console.log('ServiceManager cleaned up');
  }
}
