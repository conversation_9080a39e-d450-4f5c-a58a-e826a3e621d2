import { <PERSON><PERSON>erWindow } from 'electron';
import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

export interface GlobalEventProtectorConfig {
  enableKeyboardProtection?: boolean;
  enableMouseProtection?: boolean;
  enableGlobalMouseListener?: boolean;
  enableDebugLog?: boolean;
}

/**
 * 🛡️ 全局事件保护器
 * 复制reverse-analysis的KeyboardProtector和GlobalMouseListener功能
 */
export class GlobalEventProtector {
  private config: GlobalEventProtectorConfig;
  private isKeyboardActive = false;
  private isMouseActive = false;
  private sharpHookProcess: ChildProcess | null = null;
  private globalMouseListener: any = null;
  private uiohook: any = null;

  constructor(config: GlobalEventProtectorConfig = {}) {
    this.config = {
      enableKeyboardProtection: true,
      enableMouseProtection: true,
      enableGlobalMouseListener: true,
      enableDebugLog: true,
      ...config
    };
  }

  /**
   * 🚀 初始化全局事件保护器
   */
  async initialize(): Promise<void> {
    try {
      console.log('🛡️ Initializing GlobalEventProtector...');

      if (this.config.enableKeyboardProtection) {
        await this.initializeKeyboardProtector();
      }

      if (this.config.enableMouseProtection) {
        await this.initializeMouseProtector();
      }

      console.log('🛡️ GlobalEventProtector initialized successfully');
    } catch (error) {
      console.error('🛡️❌ Failed to initialize GlobalEventProtector:', error);
    }
  }

  /**
   * ⌨️ 初始化键盘保护器 - 复制reverse-analysis的KeyboardProtector
   */
  private async initializeKeyboardProtector(): Promise<void> {
    try {
      if (this.isKeyboardActive) {
        console.log('🛡️ Keyboard protector already running');
        return;
      }

      console.log(`🛡️ Starting keyboard protection (${process.platform} ${process.arch})`);
      
      const sharpHookPath = this.getSharpHookPath();
      if (!sharpHookPath) {
        console.error('🛡️ Keyboard protection failed: SharpHookProtector not found');
        if (process.platform === 'darwin') {
          console.error('🛡️ macOS solution:');
          console.error('  1. Ensure bin/macos-arm64/SharpHookProtector file exists');
          console.error('  2. Grant accessibility permissions in System Preferences');
        }
        return;
      }

      // 启动SharpHookProtector进程
      this.sharpHookProcess = spawn(sharpHookPath, [], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      this.sharpHookProcess.stdout?.on('data', (data) => {
        const output = data.toString().trim();
        if (output && this.config.enableDebugLog) {
          const lines = output.split('\n');
          for (const line of lines) {
            if (line.includes('拦截到') || line.includes('事件已抑制') || line.includes('键盘钩子已启用')) {
              console.log(`🛡️ [SHARP-HOOK] ${line}`);
            } else if (line.includes('启动') || line.includes('保护') || line.includes('平台')) {
              console.log(`🛡️ [SHARP-HOOK] ${line}`);
            }
          }
        }
      });

      this.sharpHookProcess.stderr?.on('data', (data) => {
        const error = data.toString().trim();
        if (error) {
          console.error(`🛡️ [SHARP-HOOK] Error: ${error}`);
        }
      });

      this.sharpHookProcess.on('close', (code) => {
        console.log(`🛡️ [SHARP-HOOK] Process exited with code ${code}`);
        this.isKeyboardActive = false;
        this.sharpHookProcess = null;
      });

      this.sharpHookProcess.on('error', (error) => {
        console.error('🛡️ [SHARP-HOOK] Process error:', error);
        this.isKeyboardActive = false;
        this.sharpHookProcess = null;
      });

      this.isKeyboardActive = true;
      console.log('🛡️ Keyboard protection started successfully');
    } catch (error) {
      console.error('🛡️ Failed to initialize keyboard protector:', error);
    }
  }

  /**
   * 🖱️ 初始化鼠标保护器 - 复制reverse-analysis的GlobalMouseListener
   */
  private async initializeMouseProtector(): Promise<void> {
    try {
      if (!this.isGlobalMouseListenerSupported()) {
        console.warn('🖱️ Global mouse listener not supported - uiohook-napi not available');
        console.log('🖱️ Falling back to standard click handling mode');
        return;
      }

      if (this.isMouseActive) {
        console.log('🖱️ Global mouse listener already running');
        return;
      }

      // 创建全局鼠标监听器
      this.globalMouseListener = this.createGlobalMouseListener({
        enableDebugLog: this.config.enableDebugLog,
        onGlobalClick: (data: any) => {
          if (this.config.enableDebugLog) {
            console.log('🖱️ Global click detected:', data);
          }

          // 🎯 处理区域截图选择点击
          this.handleAreaSelectionClick(data.x, data.y);
        }
      });

      const success = this.globalMouseListener.start();
      if (success) {
        this.isMouseActive = true;
        console.log('🖱️ Global mouse listener started successfully');
      } else {
        this.globalMouseListener = null;
        console.error('🖱️ Failed to start global mouse listener');
      }
    } catch (error) {
      console.error('🖱️ Failed to initialize mouse protector:', error);
    }
  }

  /**
   * 📁 获取SharpHookProtector路径
   */
  private getSharpHookPath(): string | null {
    try {
      let binPath: string;
      
      if (process.platform === 'darwin') {
        if (process.arch === 'arm64') {
          binPath = path.join(__dirname, '../../bin/macos-arm64/SharpHookProtector');
        } else {
          binPath = path.join(__dirname, '../../bin/macos-x64/SharpHookProtector');
        }
      } else if (process.platform === 'win32') {
        binPath = path.join(__dirname, '../../bin/windows/SharpHookProtector.exe');
      } else {
        console.log('🛡️ Unsupported platform for keyboard protection');
        return null;
      }

      if (fs.existsSync(binPath)) {
        return binPath;
      } else {
        console.log(`🛡️ SharpHookProtector not found at: ${binPath}`);
        return null;
      }
    } catch (error) {
      console.error('🛡️ Error getting SharpHookProtector path:', error);
      return null;
    }
  }

  /**
   * 🖱️ 检查是否支持全局鼠标监听
   */
  private isGlobalMouseListenerSupported(): boolean {
    try {
      require('uiohook-napi');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 🖱️ 创建全局鼠标监听器
   */
  private createGlobalMouseListener(options: any): any {
    try {
      const { uIOhook } = require('uiohook-napi');
      this.uiohook = uIOhook;

      return {
        start: () => {
          try {
            this.uiohook.start();
            
            this.uiohook.on('click', (e: any) => {
              if (options.onGlobalClick) {
                options.onGlobalClick({
                  x: e.x,
                  y: e.y,
                  button: e.button,
                  clicks: e.clicks
                });
              }
            });

            return true;
          } catch (error) {
            console.error('🖱️ Failed to start uiohook:', error);
            return false;
          }
        },
        stop: () => {
          try {
            if (this.uiohook) {
              this.uiohook.stop();
            }
          } catch (error) {
            console.error('🖱️ Failed to stop uiohook:', error);
          }
        },
        isRunning: () => {
          return this.isMouseActive;
        }
      };
    } catch (error) {
      console.error('🖱️ Failed to create global mouse listener:', error);
      return null;
    }
  }

  /**
   * 🛑 停止键盘保护
   */
  stopKeyboardProtection(): void {
    if (this.sharpHookProcess) {
      try {
        this.sharpHookProcess.kill();
        this.sharpHookProcess = null;
        this.isKeyboardActive = false;
        console.log('🛡️ Keyboard protection stopped');
      } catch (error) {
        console.error('🛡️ Failed to stop keyboard protection:', error);
      }
    }
  }

  /**
   * 🛑 停止鼠标保护
   */
  stopMouseProtection(): void {
    if (this.globalMouseListener) {
      try {
        this.globalMouseListener.stop();
        this.globalMouseListener = null;
        this.isMouseActive = false;
        console.log('🖱️ Mouse protection stopped');
      } catch (error) {
        console.error('🖱️ Failed to stop mouse protection:', error);
      }
    }
  }

  /**
   * 📊 获取保护状态
   */
  getProtectionStatus() {
    return {
      keyboard: {
        active: this.isKeyboardActive,
        supported: process.platform === 'darwin' || process.platform === 'win32'
      },
      mouse: {
        active: this.isMouseActive,
        supported: this.isGlobalMouseListenerSupported()
      }
    };
  }

  /**
   * 🎯 处理区域截图选择点击
   */
  private handleAreaSelectionClick(x: number, y: number): void {
    try {
      // 获取截图测试服务实例（通过全局变量或其他方式）
      const { ipcMain } = require('electron');

      // 发送点击事件给主进程处理
      ipcMain.emit('area-selection-click', null, { x, y });
    } catch (error) {
      console.error('❌ Failed to handle area selection click:', error);
    }
  }

  /**
   * 🧹 清理资源
   */
  destroy(): void {
    this.stopKeyboardProtection();
    this.stopMouseProtection();
    console.log('🛡️ GlobalEventProtector destroyed');
  }
}
