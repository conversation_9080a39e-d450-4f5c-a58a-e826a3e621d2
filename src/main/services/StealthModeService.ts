import { BrowserWindow, globalShortcut } from 'electron';

export enum StealthLevel {
  NORMAL = 0,      // 正常模式
  BASIC = 1,       // 基础隐身
  ENHANCED = 2,    // 增强隐身
  SUPER = 3        // 超级隐身
}

export interface StealthConfig {
  enableGlobalShortcuts?: boolean;
  defaultStealthLevel?: StealthLevel;
  enableWindowPositionControl?: boolean;
  enableTransparencyControl?: boolean;
  enableMousePassthrough?: boolean;
}

export interface WindowStealthState {
  level: StealthLevel;
  isHidden: boolean;
  originalPosition?: { x: number; y: number };
  originalSize?: { width: number; height: number };
  originalOpacity?: number;
}

// reverse-analysis的窗口状态管理
interface WindowState {
  mainWindow: BrowserWindow | null;
  isWindowVisible: boolean;
  windowPosition: { x: number; y: number } | null;
  windowSize: { width: number; height: number } | null;
  currentX: number;
  currentY: number;
}

/**
 * 🥷 隐身模式服务
 * 提供全局快捷键控制和高级隐身功能
 */
export class StealthModeService {
  private config: StealthConfig;
  private mainWindowState: WindowStealthState;
  private floatingWindowState: WindowStealthState;
  private mainWindow: BrowserWindow | null = null;
  private floatingWindow: BrowserWindow | null = null;
  private isInitialized = false;

  // reverse-analysis的完整状态管理
  private state: WindowState = {
    mainWindow: null,
    isWindowVisible: true,
    windowPosition: null,
    windowSize: null,
    currentX: 0,
    currentY: 0
  };

  constructor(config: StealthConfig = {}) {
    this.config = {
      enableGlobalShortcuts: true,
      defaultStealthLevel: StealthLevel.BASIC,
      enableWindowPositionControl: true,
      enableTransparencyControl: true,
      enableMousePassthrough: false,
      ...config
    };

    this.mainWindowState = {
      level: StealthLevel.NORMAL,
      isHidden: false
    };

    this.floatingWindowState = {
      level: StealthLevel.NORMAL,
      isHidden: false
    };
  }

  /**
   * 🚀 初始化隐身服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('🥷 StealthModeService already initialized');
      return;
    }

    try {
      if (this.config.enableGlobalShortcuts) {
        this.registerGlobalShortcuts();
      }
      
      this.isInitialized = true;
      console.log('🥷 StealthModeService initialized successfully');
    } catch (error) {
      console.error('🥷❌ Failed to initialize StealthModeService:', error);
      throw error;
    }
  }

  /**
   * 🎮 注册全局快捷键
   */
  private registerGlobalShortcuts(): void {
    try {
      // Cmd/Ctrl + H: 切换主窗口隐身状态
      globalShortcut.register('CommandOrControl+H', () => {
        this.toggleMainWindowStealth();
      });

      // Cmd/Ctrl + F: 切换浮动窗口隐身状态
      globalShortcut.register('CommandOrControl+F', () => {
        this.toggleFloatingWindowStealth();
      });

      // Cmd/Ctrl + B: 整体可见性切换
      globalShortcut.register('CommandOrControl+B', () => {
        this.toggleOverallVisibility();
      });

      // Cmd/Ctrl + S: 超级隐身模式
      globalShortcut.register('CommandOrControl+S', () => {
        this.toggleSuperStealthMode();
      });

      // 窗口位置控制
      if (this.config.enableWindowPositionControl) {
        globalShortcut.register('CommandOrControl+Up', () => {
          this.moveActiveWindow('up');
        });
        globalShortcut.register('CommandOrControl+Down', () => {
          this.moveActiveWindow('down');
        });
        globalShortcut.register('CommandOrControl+Left', () => {
          this.moveActiveWindow('left');
        });
        globalShortcut.register('CommandOrControl+Right', () => {
          this.moveActiveWindow('right');
        });
      }

      // 窗口大小控制
      globalShortcut.register('CommandOrControl+Plus', () => {
        this.resizeActiveWindow('larger');
      });
      globalShortcut.register('CommandOrControl+-', () => {
        this.resizeActiveWindow('smaller');
      });

      // 透明度控制
      if (this.config.enableTransparencyControl) {
        globalShortcut.register('CommandOrControl+[', () => {
          this.adjustActiveWindowOpacity('increase');
        });
        globalShortcut.register('CommandOrControl+]', () => {
          this.adjustActiveWindowOpacity('decrease');
        });
      }

      // 🖱️ 鼠标穿透控制 (Cmd/Ctrl + M) - 解决穿透后无法切换回来的问题！
      globalShortcut.register('CommandOrControl+M', () => {
        this.toggleMousePassthroughGlobal();
      });

      // 🎯 面试OCR功能 (Cmd/Ctrl + Shift + C) - 全屏截图面试分析
      globalShortcut.register('CommandOrControl+Shift+C', () => {
        this.triggerInterviewOCR('fullscreen');
      });

      // 🎯 面试OCR功能 (Cmd/Ctrl + Shift + A) - 区域截图面试分析
      globalShortcut.register('CommandOrControl+Shift+A', () => {
        this.triggerInterviewOCR('area');
      });

      console.log('🥷 Global shortcuts registered successfully');
    } catch (error) {
      console.error('🥷❌ Failed to register global shortcuts:', error);
    }
  }

  /**
   * 🪟 设置窗口引用 - 集成reverse-analysis状态管理
   */
  setMainWindow(window: BrowserWindow | null): void {
    this.mainWindow = window;
    this.state.mainWindow = window; // reverse-analysis状态同步

    if (window) {
      this.saveWindowState(window, this.mainWindowState);
      // reverse-analysis的窗口初始化
      const bounds = window.getBounds();
      this.state.currentX = bounds.x;
      this.state.currentY = bounds.y;
      this.state.isWindowVisible = true;
      console.log(`🕳️ Window initialized at position: ${bounds.x}, ${bounds.y} (reverse-analysis method)`);
    }
  }

  setFloatingWindow(window: BrowserWindow | null): void {
    this.floatingWindow = window;
    if (window) {
      this.saveWindowState(window, this.floatingWindowState);
    }
  }

  /**
   * 💾 保存窗口状态
   */
  private saveWindowState(window: BrowserWindow, state: WindowStealthState): void {
    const bounds = window.getBounds();
    state.originalPosition = { x: bounds.x, y: bounds.y };
    state.originalSize = { width: bounds.width, height: bounds.height };
    state.originalOpacity = window.getOpacity();
  }

  /**
   * 🔄 刷新窗口引用
   */
  private refreshWindowReferences(): void {
    const { BrowserWindow } = require('electron');
    const windows = BrowserWindow.getAllWindows();

    // 查找主窗口（通常是较大的窗口或包含特定标题的窗口）
    if (!this.mainWindow || this.mainWindow.isDestroyed()) {
      this.mainWindow = windows.find(win =>
        !win.isDestroyed() &&
        (win.getTitle().includes('Geek') || win.getBounds().width > 500)
      ) || windows[0] || null;
    }

    // 查找浮动窗口（通常是较小的窗口）
    if (!this.floatingWindow || this.floatingWindow.isDestroyed()) {
      this.floatingWindow = windows.find(win =>
        !win.isDestroyed() &&
        win.getBounds().width <= 500 &&
        win !== this.mainWindow
      ) || null;
    }
  }

  /**
   * 🔄 切换主窗口隐身状态 - 完整的reverse-analysis实现！
   */
  toggleMainWindowStealth(): void {
    // 确保状态同步
    if (this.mainWindow && !this.state.mainWindow) {
      this.state.mainWindow = this.mainWindow;
    }

    // 🕳️ 使用reverse-analysis的完整toggleMainWindow实现
    this.toggleMainWindowComplete();

    // 同步状态
    this.mainWindowState.isHidden = !this.state.isWindowVisible;
    this.mainWindowState.level = this.state.isWindowVisible ? StealthLevel.NORMAL : StealthLevel.SUPER;
  }

  /**
   * 🔄 切换浮动窗口隐身状态 - reverse-analysis真正的方法！
   */
  toggleFloatingWindowStealth(): void {
    // 尝试获取当前的浮动窗口
    if (!this.floatingWindow) {
      const { BrowserWindow } = require('electron');
      const windows = BrowserWindow.getAllWindows();
      this.floatingWindow = windows.find(win => !win.isDestroyed() && win.getBounds().width < 500) || null;
    }

    if (!this.floatingWindow) {
      console.log('🥷 Floating window not available');
      return;
    }

    // 🕳️ reverse-analysis的真正方法：直接隐藏/显示窗口！
    if (this.floatingWindowState.isHidden) {
      this.floatingWindow.show();
      this.floatingWindowState.isHidden = false;
      this.floatingWindowState.level = StealthLevel.NORMAL;
      console.log('🕳️ Floating window restored from complete hiding (reverse-analysis method)');
    } else {
      this.floatingWindow.hide();
      this.floatingWindowState.isHidden = true;
      this.floatingWindowState.level = StealthLevel.SUPER;
      console.log('🕳️ Floating window completely hidden (reverse-analysis method)');
    }
  }

  /**
   * 🔄 切换整体可见性 - 完整的reverse-analysis实现！
   */
  toggleOverallVisibility(): void {
    // 🕳️ 主要使用reverse-analysis的完整方法处理主窗口
    if (this.mainWindow) {
      this.state.mainWindow = this.mainWindow;
      this.toggleMainWindowComplete();
      this.mainWindowState.isHidden = !this.state.isWindowVisible;
    }

    // 浮动窗口使用简单的hide/show
    if (this.floatingWindow && !this.floatingWindow.isDestroyed()) {
      if (this.floatingWindowState.isHidden) {
        this.floatingWindow.show();
        this.floatingWindowState.isHidden = false;
        console.log('🕳️ Floating window restored to visible');
      } else {
        this.floatingWindow.hide();
        this.floatingWindowState.isHidden = true;
        console.log('🕳️ Floating window completely hidden (reverse-analysis method)');
      }
    }

    const mainHidden = this.mainWindowState.isHidden;
    const floatingHidden = this.floatingWindowState.isHidden;
    console.log(`🕳️ Overall visibility: Main=${mainHidden ? 'HIDDEN' : 'VISIBLE'}, Floating=${floatingHidden ? 'HIDDEN' : 'VISIBLE'} (reverse-analysis method)`);
  }

  /**
   * 🔄 切换超级隐身模式 - 完整的reverse-analysis实现！
   */
  toggleSuperStealthMode(): void {
    // 🕳️ 主窗口使用reverse-analysis的完整方法
    if (this.mainWindow) {
      this.state.mainWindow = this.mainWindow;
      this.toggleMainWindowComplete();
      this.mainWindowState.isHidden = !this.state.isWindowVisible;
      this.mainWindowState.level = this.state.isWindowVisible ? StealthLevel.NORMAL : StealthLevel.SUPER;
    }

    // 浮动窗口使用简单方法
    if (this.floatingWindow && !this.floatingWindow.isDestroyed()) {
      if (this.floatingWindowState.isHidden) {
        this.floatingWindow.show();
        this.floatingWindowState.isHidden = false;
        this.floatingWindowState.level = StealthLevel.NORMAL;
      } else {
        this.floatingWindow.hide();
        this.floatingWindowState.isHidden = true;
        this.floatingWindowState.level = StealthLevel.SUPER;
      }
    }

    const mainHidden = this.mainWindowState.isHidden;
    const floatingHidden = this.floatingWindowState.isHidden;
    console.log(`🕳️ Super stealth mode: Main=${mainHidden ? 'HIDDEN' : 'VISIBLE'}, Floating=${floatingHidden ? 'HIDDEN' : 'VISIBLE'} (reverse-analysis method)`);
  }

  /**
   * 🎯 应用隐身级别
   */
  private applyStealthLevel(window: BrowserWindow, state: WindowStealthState, level: StealthLevel): void {
    state.level = level;

    switch (level) {
      case StealthLevel.NORMAL:
        this.applyNormalMode(window, state);
        break;
      case StealthLevel.BASIC:
        this.applyBasicStealth(window, state);
        break;
      case StealthLevel.ENHANCED:
        this.applyEnhancedStealth(window, state);
        break;
      case StealthLevel.SUPER:
        this.applySuperStealth(window, state);
        break;
    }
  }

  /**
   * 🔧 应用不同隐身模式
   */
  private applyNormalMode(window: BrowserWindow, state: WindowStealthState): void {
    // 恢复正常状态
    if (state.originalOpacity !== undefined) {
      window.setOpacity(state.originalOpacity);
    }
    window.setSkipTaskbar(false);
    window.show();
    state.isHidden = false;
  }

  private applyBasicStealth(window: BrowserWindow, state: WindowStealthState): void {
    // 基础隐身：降低透明度，跳过任务栏
    window.setOpacity(0.3);
    window.setSkipTaskbar(true);
  }

  private applyEnhancedStealth(window: BrowserWindow, state: WindowStealthState): void {
    // 增强隐身：更低透明度，置顶，跳过任务栏
    window.setOpacity(0.1);
    window.setSkipTaskbar(true);
    window.setAlwaysOnTop(true);
  }

  private applySuperStealth(window: BrowserWindow, state: WindowStealthState): void {
    // 🕳️ 使用高级窗口隐藏器实现真正的reverse-analysis级别隐身
    // 注意：这里我们需要通过事件系统来调用高级隐藏器，因为它在PrivacyProtectionService中

    // 发送事件请求高级隐身
    const windows = require('electron').BrowserWindow.getAllWindows();
    windows.forEach((win: any) => {
      win.webContents.send('apply-super-stealth-requested', window.id);
    });

    // 同时应用基础超级隐身作为备用
    window.setOpacity(0.001); // 更低的透明度
    window.setSkipTaskbar(true);
    window.setAlwaysOnTop(true);

    // 平台特定的超级隐身设置
    if (process.platform === 'darwin') {
      try {
        // @ts-ignore - macOS specific API
        window.setLevel?.('screen-saver'); // 屏幕保护程序级别
        // @ts-ignore - macOS specific API
        window.setWindowButtonVisibility?.(false);
        // @ts-ignore - macOS specific API
        window.setVisibleOnAllWorkspaces?.(false);
      } catch (error) {
        console.log('🥷 macOS specific stealth features not available:', error);
      }
    }

    console.log('🕳️ Super stealth mode activated with reverse-analysis level hiding');
  }

  /**
   * 🏃 窗口移动控制
   */
  private moveActiveWindow(direction: 'up' | 'down' | 'left' | 'right'): void {
    const window = this.getActiveWindow();
    if (!window) return;

    const bounds = window.getBounds();
    const step = 20; // 移动步长

    switch (direction) {
      case 'up':
        window.setBounds({ ...bounds, y: bounds.y - step });
        break;
      case 'down':
        window.setBounds({ ...bounds, y: bounds.y + step });
        break;
      case 'left':
        window.setBounds({ ...bounds, x: bounds.x - step });
        break;
      case 'right':
        window.setBounds({ ...bounds, x: bounds.x + step });
        break;
    }
  }

  /**
   * 📏 窗口大小控制
   */
  private resizeActiveWindow(action: 'larger' | 'smaller'): void {
    const window = this.getActiveWindow();
    if (!window) return;

    const bounds = window.getBounds();
    const step = 50; // 大小调整步长

    if (action === 'larger') {
      window.setBounds({
        ...bounds,
        width: bounds.width + step,
        height: bounds.height + step
      });
    } else {
      window.setBounds({
        ...bounds,
        width: Math.max(200, bounds.width - step),
        height: Math.max(150, bounds.height - step)
      });
    }
  }

  /**
   * 🌫️ 透明度控制
   */
  private adjustActiveWindowOpacity(action: 'increase' | 'decrease'): void {
    const window = this.getActiveWindow();
    if (!window) return;

    const currentOpacity = window.getOpacity();
    const step = 0.1;

    if (action === 'increase') {
      window.setOpacity(Math.min(1.0, currentOpacity + step));
    } else {
      window.setOpacity(Math.max(0.1, currentOpacity - step));
    }
  }

  /**
   * 🖱️ 切换鼠标穿透模式 - 完整的reverse-analysis实现！
   */
  toggleMousePassthrough(): void {
    const window = this.getActiveWindow();
    if (!window) {
      console.log('🖱️ No active window for mouse passthrough');
      return;
    }

    // 🕳️ reverse-analysis的真正鼠标穿透实现！
    if (this.config.enableMousePassthrough) {
      // 禁用鼠标穿透
      this.disableClickThrough(window);
      this.config.enableMousePassthrough = false;
      console.log('🖱️ Mouse passthrough DISABLED - window is now interactive (reverse-analysis method)');
    } else {
      // 启用鼠标穿透
      this.enableClickThrough(window);
      this.config.enableMousePassthrough = true;
      console.log('🖱️ Mouse passthrough ENABLED - clicks will pass through window (reverse-analysis method)');
    }
  }

  /**
   * 🖱️ 全局鼠标穿透切换 - 解决穿透后无法切换回来的问题！
   */
  toggleMousePassthroughGlobal(): void {
    // 获取所有窗口，确保能找到目标窗口
    const { BrowserWindow } = require('electron');
    const windows = BrowserWindow.getAllWindows();
    const targetWindow = windows.find(win => !win.isDestroyed() && win.getTitle().includes('Geek')) || windows[0];

    if (!targetWindow) {
      console.log('🖱️ No window available for global mouse passthrough toggle');
      return;
    }

    // 🕳️ 直接切换鼠标穿透状态
    if (this.config.enableMousePassthrough) {
      // 禁用鼠标穿透
      targetWindow.setIgnoreMouseEvents(false);
      targetWindow.setContentProtection(true); // 保持屏幕保护
      if (!targetWindow.isFocused()) {
        targetWindow.focus();
      }
      this.config.enableMousePassthrough = false;
      console.log('🖱️ Global mouse passthrough DISABLED - window is now interactive (Cmd+M)');

      // 通知前端更新状态
      targetWindow.webContents.send('mouse-passthrough-changed', false);
    } else {
      // 启用鼠标穿透
      targetWindow.setIgnoreMouseEvents(true, { forward: true });
      targetWindow.setContentProtection(true); // 保持屏幕保护
      this.config.enableMousePassthrough = true;
      console.log('🖱️ Global mouse passthrough ENABLED - clicks will pass through window (Cmd+M)');

      // 通知前端更新状态
      targetWindow.webContents.send('mouse-passthrough-changed', true);
    }
  }

  /**
   * 🕳️ 启用鼠标穿透 - 完全复制reverse-analysis的enableClickThrough
   */
  private enableClickThrough(window: BrowserWindow): void {
    if (!window || window.isDestroyed() || !this.state.isWindowVisible) {
      console.log('🖱️ Cannot enable click-through: window not available or not visible');
      return;
    }

    // 检查是否支持全局鼠标监听（这里简化检查）
    const isGlobalMouseListenerSupported = true; // 我们已经安装了uiohook-napi

    if (!isGlobalMouseListenerSupported) {
      console.log('🔄 Advanced click-through not supported, keeping window interactive');
      return;
    }

    if (process.platform === 'win32' || process.platform === 'darwin') {
      // 🕳️ 关键：这就是reverse-analysis的真正实现！
      window.setIgnoreMouseEvents(true, { forward: true });

      // 🛡️ reverse-analysis的屏幕共享保护！
      window.setContentProtection(true);

      console.log('🖱️ Click-through enabled with event forwarding (reverse-analysis method)');
      console.log('🛡️ Content protection enabled - screen sharing protection active');
    }
  }

  /**
   * 🕳️ 禁用鼠标穿透 - 完全复制reverse-analysis的disableClickThrough
   */
  private disableClickThrough(window: BrowserWindow): void {
    if (!window || window.isDestroyed() || !this.state.isWindowVisible) {
      console.log('🖱️ Cannot disable click-through: window not available or not visible');
      return;
    }

    if (process.platform === 'win32' || process.platform === 'darwin') {
      // 🕳️ 关键：恢复正常鼠标事件处理
      window.setIgnoreMouseEvents(false);

      // 🛡️ 保持屏幕共享保护（根据reverse-analysis的实现）
      window.setContentProtection(true);

      if (!window.isFocused()) {
        window.focus();
      }
      console.log('🖱️ Click-through disabled, window is now interactive (reverse-analysis method)');
      console.log('🛡️ Content protection maintained - screen sharing protection still active');
    }
  }

  /**
   * 🎯 获取当前活动窗口
   */
  private getActiveWindow(): BrowserWindow | null {
    // 优先返回浮动窗口（如果存在且可见）
    if (this.floatingWindow && !this.floatingWindowState.isHidden) {
      return this.floatingWindow;
    }
    // 否则返回主窗口
    return this.mainWindow;
  }

  /**
   * 📊 获取当前状态
   */
  getStealthStatus() {
    return {
      mainWindow: {
        level: StealthLevel[this.mainWindowState.level],
        isHidden: this.mainWindowState.isHidden
      },
      floatingWindow: {
        level: StealthLevel[this.floatingWindowState.level],
        isHidden: this.floatingWindowState.isHidden
      },
      isInitialized: this.isInitialized
    };
  }

  /**
   * 🕳️ reverse-analysis的完整hideMainWindow实现
   */
  private hideMainWindow(): void {
    if (!this.state.mainWindow) {
      console.warn("🕳️ hideMainWindow: mainWindow is null, cannot hide window");
      return;
    }

    if (this.state.mainWindow.isDestroyed()) {
      console.warn("🕳️ hideMainWindow: mainWindow is destroyed, cannot hide window");
      this.state.mainWindow = null;
      return;
    }

    try {
      const bounds = this.state.mainWindow.getBounds();
      this.state.windowPosition = { x: bounds.x, y: bounds.y };
      this.state.windowSize = { width: bounds.width, height: bounds.height };

      // 🕳️ 关键：完全隐藏窗口！
      this.state.mainWindow.hide();
      this.state.isWindowVisible = false;

      console.log("🕳️ Window hidden completely (reverse-analysis method)");
    } catch (error) {
      console.error("🕳️ Error in hideMainWindow:", error);
      if (this.state.mainWindow && this.state.mainWindow.isDestroyed()) {
        this.state.mainWindow = null;
      }
    }
  }

  /**
   * 🕳️ reverse-analysis的完整showMainWindow实现
   */
  private showMainWindow(): void {
    if (!this.state.mainWindow) {
      console.warn("🕳️ showMainWindow: mainWindow is null, cannot show window");
      return;
    }

    if (this.state.mainWindow.isDestroyed()) {
      console.warn("🕳️ showMainWindow: mainWindow is destroyed, cannot show window");
      this.state.mainWindow = null;
      return;
    }

    try {
      // 恢复窗口位置和大小
      if (this.state.windowPosition && this.state.windowSize) {
        this.state.mainWindow.setBounds({
          ...this.state.windowPosition,
          ...this.state.windowSize
        });
      }

      // 🛡️ reverse-analysis的屏幕共享保护 - 完全按照源码实现
      if (this.state.mainWindow && !this.state.mainWindow.isDestroyed()) {
        this.state.mainWindow.setContentProtection(true);
        console.log('🛡️ Content protection enabled - screen sharing blocked (reverse-analysis method)');
      }

      // 🕳️ 关键：显示窗口但不激活
      if (this.state.mainWindow && !this.state.mainWindow.isDestroyed()) {
        this.state.mainWindow.showInactive();
      }

      // 恢复透明度
      if (this.state.mainWindow && !this.state.mainWindow.isDestroyed()) {
        const savedOpacity = this.mainWindowState.originalOpacity || 1.0;
        this.state.mainWindow.setOpacity(savedOpacity);
      }

      this.state.isWindowVisible = true;
      console.log("🕳️ Window shown completely (reverse-analysis method)");
    } catch (error) {
      console.error("🕳️ Error in showMainWindow:", error);
      if (this.state.mainWindow && this.state.mainWindow.isDestroyed()) {
        this.state.mainWindow = null;
      }
    }
  }

  /**
   * 🕳️ reverse-analysis的完整toggleMainWindow实现
   */
  private toggleMainWindowComplete(): void {
    console.log(`🕳️ Toggling window. Current state: ${this.state.isWindowVisible ? "visible" : "hidden"} (reverse-analysis method)`);

    if (!this.state.mainWindow) {
      console.log("🕳️ toggleMainWindow: mainWindow is null");
      return;
    }

    if (this.state.isWindowVisible) {
      this.hideMainWindow();
    } else {
      this.showMainWindow();
    }
  }

  /**
   * 🎯 触发面试OCR功能
   */
  private triggerInterviewOCR(type: 'fullscreen' | 'area'): void {
    try {
      console.log(`🎯 Triggering interview OCR: ${type}`);

      // 发送IPC事件到主进程
      const { ipcMain } = require('electron');
      ipcMain.emit('trigger-interview-ocr', null, { type });
    } catch (error) {
      console.error('❌ Failed to trigger interview OCR:', error);
    }
  }

  /**
   * 🧹 清理资源
   */
  destroy(): void {
    if (this.config.enableGlobalShortcuts) {
      globalShortcut.unregisterAll();
    }
    this.isInitialized = false;
    console.log('🥷 StealthModeService destroyed');
  }
}
