import { BrowserWindow, screen } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { execFile } from 'child_process';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

const execFileAsync = promisify(execFile);

/**
 * 🖼️ reverse-analysis风格的截图服务 - 完全按照源码实现
 */
export class ReverseAnalysisScreenshotService {
  private screenshotDir: string;
  private tempDir: string;
  private screenshotMode: 'fullscreen' | 'area' = 'fullscreen';
  private areaSelectionState: {
    isSelecting: boolean;
    startPoint: { x: number; y: number } | null;
    endPoint: { x: number; y: number } | null;
    tipWindow: BrowserWindow | null;
    resolve?: (value: any) => void;
    reject?: (error: any) => void;
  };

  constructor() {
    this.screenshotDir = path.join(os.tmpdir(), 'geek-assistant-screenshots');
    this.tempDir = path.join(os.tmpdir(), 'geek-assistant-temp');
    this.ensureDirectoriesExist();
    
    this.areaSelectionState = {
      isSelecting: false,
      startPoint: null,
      endPoint: null,
      tipWindow: null
    };
  }

  /**
   * 确保目录存在
   */
  private ensureDirectoriesExist(): void {
    [this.screenshotDir, this.tempDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log('📁 Directory created:', dir);
      }
    });
  }

  /**
   * 🎯 设置截图模式
   */
  setScreenshotMode(mode: 'fullscreen' | 'area'): void {
    this.screenshotMode = mode;
    console.log(`📸 Screenshot mode set to: ${mode}`);
  }

  /**
   * 🖼️ 主要截图方法 - 完全按照reverse-analysis实现
   */
  async takeScreenshot(): Promise<string> {
    console.log(`📸 Taking screenshot in mode: ${this.screenshotMode}`);

    try {
      let screenshotBuffer: Buffer;

      if (this.screenshotMode === 'area') {
        console.log('🎯 启动局部截图模式...');
        console.log('🎯 等待用户选择区域...');

        const selectedArea = await this.startAreaSelection();
        console.log('🎯 区域选择结果:', selectedArea);

        if (!selectedArea) {
          console.log('❌ 用户取消了区域选择');
          throw new Error('Screenshot selection canceled');
        }

        console.log('✅ 区域选择成功，准备截图...');

        // 延迟让窗口完全隐藏
        const hideDelay = process.platform === 'win32' ? 300 : 200;
        console.log(`⏰ 等待窗口隐藏 ${hideDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, hideDelay));

        console.log('📸 开始捕获区域截图...');
        screenshotBuffer = await this.captureAreaScreenshot(selectedArea);
        console.log('✅ 区域截图捕获完成');
      } else {
        console.log('📸 启动全屏截图模式...');
        // 全屏截图
        const hideDelay = process.platform === 'win32' ? 500 : 300;
        console.log(`⏰ 等待窗口隐藏 ${hideDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, hideDelay));

        console.log('📸 开始捕获全屏截图...');
        screenshotBuffer = await this.captureFullScreenshot();
        console.log('✅ 全屏截图捕获完成');
      }

      if (!screenshotBuffer || screenshotBuffer.length === 0) {
        console.error('❌ 截图缓冲区为空');
        throw new Error('Screenshot capture returned empty buffer');
      }

      // 保存截图
      const screenshotPath = path.join(this.screenshotDir, `${uuidv4()}.png`);
      console.log('💾 保存截图到:', screenshotPath);
      await fs.promises.writeFile(screenshotPath, screenshotBuffer);

      console.log('✅ Screenshot saved:', screenshotPath);
      console.log('📊 Screenshot size:', screenshotBuffer.length, 'bytes');

      return screenshotPath;
    } catch (error) {
      console.error('❌ Failed to take screenshot:', error);
      console.error('❌ Error details:', error.message);
      console.error('❌ Error stack:', error.stack);
      throw error;
    }
  }

  /**
   * 🎯 启动区域选择 - 完全按照reverse-analysis实现
   */
  private startAreaSelection(): Promise<{ x: number; y: number; width: number; height: number } | null> {
    return new Promise((resolve, reject) => {
      try {
        const tipWindow = this.createTipWindow();
        const allWindows = BrowserWindow.getAllWindows();
        const hiddenWindows: BrowserWindow[] = [];
        
        console.log('准备隐藏所有窗口以进行局部截图选择...');
        allWindows.forEach(window => {
          if (!window.isDestroyed() && window.isVisible() && window !== tipWindow) {
            hiddenWindows.push(window);
            window.hide();
          }
        });
        console.log(`已隐藏 ${hiddenWindows.length} 个窗口`);

        this.areaSelectionState = {
          isSelecting: true,
          startPoint: null,
          endPoint: null,
          tipWindow,
          resolve: (value) => {
            if (tipWindow && !tipWindow.isDestroyed()) {
              tipWindow.close();
            }
            console.log('恢复被隐藏的窗口...');
            hiddenWindows.forEach(window => {
              if (!window.isDestroyed()) {
                window.show();
              }
            });
            resolve(value);
          },
          reject: (error) => {
            if (tipWindow && !tipWindow.isDestroyed()) {
              tipWindow.close();
            }
            hiddenWindows.forEach(window => {
              if (!window.isDestroyed()) {
                window.show();
              }
            });
            reject(error);
          }
        };

        // 设置超时（增加到60秒）
        setTimeout(() => {
          if (this.areaSelectionState.isSelecting) {
            console.log('⏰ Area selection timeout (60s)');
            this.cancelAreaSelection();
          }
        }, 60000); // 60秒超时

      } catch (error) {
        console.error('❌ Failed to start area selection:', error);
        reject(error);
      }
    });
  }

  /**
   * 🎨 创建提示窗口 - 完全按照reverse-analysis实现
   */
  private createTipWindow(): BrowserWindow {
    try {
      const primaryDisplay = screen.getPrimaryDisplay();
      const { width: screenWidth } = primaryDisplay.workAreaSize;
      
      const tipWindow = new BrowserWindow({
        width: 320,
        height: 100,
        x: Math.floor((screenWidth - 320) / 2),
        y: 20,
        frame: false,
        transparent: true,
        alwaysOnTop: true,
        skipTaskbar: true,
        resizable: false,
        movable: false,
        focusable: false,
        hasShadow: true,
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false
        }
      });

      const tipHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <style>
            body {
              margin: 0;
              padding: 0;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background: rgba(0, 0, 0, 0.8);
              color: white;
              border-radius: 8px;
              overflow: hidden;
            }
            .tip-container {
              padding: 16px;
              text-align: center;
            }
            .tip-title {
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 4px;
            }
            .tip-subtitle {
              font-size: 12px;
              color: #ccc;
              margin-bottom: 8px;
            }
            .tip-progress {
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .progress-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: #666;
              margin: 0 2px;
              transition: background 0.2s;
            }
            .progress-dot.active {
              background: #00ff00;
            }
            .progress-text {
              font-size: 10px;
              color: #999;
              margin-left: 4px;
            }
          </style>
        </head>
        <body>
          <div class="tip-container">
            <div class="tip-title">🎯 面试题目截图</div>
            <div class="tip-subtitle" id="tip-text">点击题目左上角</div>
            <div class="tip-progress">
              <div class="progress-dot active" id="dot1"></div>
              <div class="progress-dot" id="dot2"></div>
              <span class="progress-text" id="progress">1/2</span>
            </div>
          </div>
          <script>
            const { ipcRenderer } = require('electron');
            
            ipcRenderer.on('update-tip', (event, hasStartPoint) => {
              const tipText = document.getElementById('tip-text');
              const dot1 = document.getElementById('dot1');
              const dot2 = document.getElementById('dot2');
              const progress = document.getElementById('progress');
              
              if (hasStartPoint) {
                tipText.textContent = '点击题目右下角';
                dot1.classList.add('active');
                dot2.classList.add('active');
                progress.textContent = '2/2';
              } else {
                tipText.textContent = '点击题目左上角';
                dot1.classList.add('active');
                dot2.classList.remove('active');
                progress.textContent = '1/2';
              }
            });
          </script>
        </body>
        </html>
      `;

      tipWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(tipHtml)}`);
      tipWindow.setIgnoreMouseEvents(true);
      
      return tipWindow;
    } catch (error) {
      console.error('创建提示窗口失败:', error);
      throw error;
    }
  }

  /**
   * 🖱️ 处理区域选择点击 - 完全按照reverse-analysis实现
   */
  handleAreaSelectionClick(x: number, y: number): boolean {
    if (!this.areaSelectionState.isSelecting) {
      return false;
    }

    console.log(`🖱️ [AREA-SCREENSHOT] Received click coordinates: (${x}, ${y}) - Platform: ${process.platform}`);

    if (!this.areaSelectionState.startPoint) {
      // 设置第一个点
      this.areaSelectionState.startPoint = { x, y };
      console.log(`📍 [AREA-SCREENSHOT] 设置起始点 (${x}, ${y})`);
      
      if (this.areaSelectionState.tipWindow && !this.areaSelectionState.tipWindow.isDestroyed()) {
        this.areaSelectionState.tipWindow.webContents.send('update-tip', true);
      }
      return true;
    } else {
      // 设置第二个点并完成选择
      this.areaSelectionState.endPoint = { x, y };
      console.log(`📍 [AREA-SCREENSHOT] 设置结束点 (${x}, ${y})`);
      
      const startPoint = this.areaSelectionState.startPoint;
      const endPoint = this.areaSelectionState.endPoint;
      
      const left = Math.min(startPoint.x, endPoint.x);
      const top = Math.min(startPoint.y, endPoint.y);
      let width = Math.abs(endPoint.x - startPoint.x);
      let height = Math.abs(endPoint.y - startPoint.y);

      // 如果区域太小，自动扩展到最小尺寸
      if (width < 50 || height < 20) {
        console.warn('⚠️ [AREA-SCREENSHOT] 选择的区域太小，自动扩展到合适尺寸');
        width = Math.max(width, 200);  // 最小宽度200像素（适合题目）
        height = Math.max(height, 100); // 最小高度100像素（适合题目）

        // 根据起始点扩展区域
        if (endPoint.x >= startPoint.x) {
          endPoint.x = startPoint.x + width;
        } else {
          endPoint.x = startPoint.x - width;
        }

        if (endPoint.y >= startPoint.y) {
          endPoint.y = startPoint.y + height;
        } else {
          endPoint.y = startPoint.y - height;
        }
      }
      
      const area = { x: left, y: top, width, height };
      console.log('✅ [AREA-SCREENSHOT] 局部截图区域选择完成:', area);
      console.log(`📐 [AREA-SCREENSHOT] 区域详情: 起始点(${startPoint.x}, ${startPoint.y}) -> 结束点(${endPoint.x}, ${endPoint.y}) = 区域(${left}, ${top}, ${width}x${height})`);
      
      if (this.areaSelectionState.resolve) {
        this.areaSelectionState.resolve(area);
      }
      
      this.areaSelectionState = {
        isSelecting: false,
        startPoint: null,
        endPoint: null,
        tipWindow: null
      };
      
      return true;
    }
  }

  /**
   * ❌ 取消区域选择
   */
  cancelAreaSelection(): void {
    if (this.areaSelectionState.isSelecting && this.areaSelectionState.resolve) {
      this.areaSelectionState.resolve(null);
    }
    
    this.areaSelectionState = {
      isSelecting: false,
      startPoint: null,
      endPoint: null,
      tipWindow: null
    };
    
    console.log('局部截图选择已取消');
  }

  /**
   * 🔍 检查是否正在进行区域选择
   */
  isAreaSelecting(): boolean {
    return this.areaSelectionState.isSelecting;
  }

  /**
   * 📊 获取当前选择状态
   */
  getAreaSelectionState(): { isSelecting: boolean; hasStartPoint: boolean } {
    return {
      isSelecting: this.areaSelectionState.isSelecting,
      hasStartPoint: this.areaSelectionState.startPoint !== null
    };
  }

  /**
   * 🖼️ 捕获全屏截图
   */
  private async captureFullScreenshot(): Promise<Buffer> {
    try {
      console.log('📸 Taking full screenshot...');

      if (process.platform === 'win32') {
        return await this.captureWindowsFullScreenshot();
      } else {
        return await this.captureMacFullScreenshot();
      }
    } catch (error) {
      console.error('❌ Failed to capture full screenshot:', error);
      throw error;
    }
  }

  /**
   * 🎯 根据指定区域进行截图 - 完全按照reverse-analysis实现
   */
  private async captureAreaScreenshot(area: { x: number; y: number; width: number; height: number }): Promise<Buffer> {
    try {
      console.log(`🖼️ [AREA-SCREENSHOT] 开始局部截图，平台: ${process.platform}`);
      console.log(`🖼️ [AREA-SCREENSHOT] 截图区域:`, area);
      console.log(`🖼️ [AREA-SCREENSHOT] 坐标验证: x=${area.x}, y=${area.y}, width=${area.width}, height=${area.height}`);

      if (process.platform === 'win32') {
        console.log(`🖼️ [AREA-SCREENSHOT] 使用Windows PowerShell截图方法`);
        return await this.captureWindowsAreaScreenshot(area);
      } else {
        console.log(`🖼️ [AREA-SCREENSHOT] 使用macOS screencapture截图方法`);
        return await this.captureMacAreaScreenshot(area);
      }
    } catch (error) {
      console.error('❌ [AREA-SCREENSHOT] 局部截图失败:', error);
      throw new Error(`局部截图失败: ${error.message}`);
    }
  }

  /**
   * 🍎 macOS全屏截图
   */
  private async captureMacFullScreenshot(): Promise<Buffer> {
    const tempFile = path.join(this.tempDir, `full-${uuidv4()}.png`);

    console.log('🍎 [MACOS-SCREENSHOT] 执行全屏截图...');
    await execFileAsync('screencapture', ['-t', 'png', '-x', tempFile]);

    if (fs.existsSync(tempFile)) {
      const buffer = await fs.promises.readFile(tempFile);
      console.log(`✅ [MACOS-SCREENSHOT] macOS全屏截图成功，大小: ${buffer.length} bytes`);

      try {
        await fs.promises.unlink(tempFile);
      } catch (err) {
        console.warn('⚠️ [MACOS-SCREENSHOT] 清理临时文件失败:', err);
      }

      return buffer;
    } else {
      throw new Error('screencapture全屏截图文件未创建');
    }
  }

  /**
   * 🍎 macOS区域截图 - 完全按照reverse-analysis实现
   */
  private async captureMacAreaScreenshot(area: { x: number; y: number; width: number; height: number }): Promise<Buffer> {
    const tempFile = path.join(this.tempDir, `area-${uuidv4()}.png`);

    console.log(`🍎 [MACOS-SCREENSHOT] 临时文件路径: ${tempFile}`);
    console.log(`🍎 [MACOS-SCREENSHOT] screencapture参数: -R ${area.x},${area.y},${area.width},${area.height}`);
    console.log(`🍎 [MACOS-SCREENSHOT] 执行screencapture命令...`);

    await execFileAsync('screencapture', [
      '-R',
      `${area.x},${area.y},${area.width},${area.height}`,
      '-t',
      'png',
      '-x', // 禁用声音
      tempFile
    ]);

    if (fs.existsSync(tempFile)) {
      const buffer = await fs.promises.readFile(tempFile);
      console.log(`✅ [MACOS-SCREENSHOT] macOS局部截图成功，大小: ${buffer.length} bytes`);

      try {
        await fs.promises.unlink(tempFile);
      } catch (err) {
        console.warn('⚠️ [MACOS-SCREENSHOT] 清理临时文件失败:', err);
      }

      return buffer;
    } else {
      throw new Error('screencapture局部截图文件未创建');
    }
  }

  /**
   * 🪟 Windows全屏截图
   */
  private async captureWindowsFullScreenshot(): Promise<Buffer> {
    const tempFile = path.join(this.tempDir, `full-${uuidv4()}.png`);

    console.log('🪟 [WINDOWS-SCREENSHOT] 执行全屏截图...');

    const psScript = `
    Add-Type -AssemblyName System.Windows.Forms,System.Drawing
    $bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds
    $bmp = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
    $graphics = [System.Drawing.Graphics]::FromImage($bmp)
    $graphics.CopyFromScreen($bounds.Left, $bounds.Top, 0, 0, $bounds.Size)
    $bmp.Save('${tempFile.replace(/\\/g, '\\\\')}', [System.Drawing.Imaging.ImageFormat]::Png)
    $graphics.Dispose()
    $bmp.Dispose()
    `;

    await execFileAsync('powershell', ['-Command', psScript]);

    if (fs.existsSync(tempFile)) {
      const buffer = await fs.promises.readFile(tempFile);
      console.log(`✅ [WINDOWS-SCREENSHOT] Windows全屏截图成功，大小: ${buffer.length} bytes`);

      try {
        await fs.promises.unlink(tempFile);
      } catch (err) {
        console.warn('⚠️ [WINDOWS-SCREENSHOT] 清理临时文件失败:', err);
      }

      return buffer;
    } else {
      throw new Error('PowerShell全屏截图文件未创建');
    }
  }

  /**
   * 🪟 Windows区域截图 - 完全按照reverse-analysis实现
   */
  private async captureWindowsAreaScreenshot(area: { x: number; y: number; width: number; height: number }): Promise<Buffer> {
    const tempFile = path.join(this.tempDir, `area-${uuidv4()}.png`);

    console.log(`🪟 [WINDOWS-SCREENSHOT] 临时文件路径: ${tempFile}`);
    console.log(`🪟 [WINDOWS-SCREENSHOT] PowerShell截图参数: Rectangle(${area.x}, ${area.y}, ${area.width}, ${area.height})`);

    const psScript = `
    Add-Type -AssemblyName System.Windows.Forms,System.Drawing
    $bounds = [System.Drawing.Rectangle]::new(${area.x}, ${area.y}, ${area.width}, ${area.height})
    $bmp = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
    $graphics = [System.Drawing.Graphics]::FromImage($bmp)
    $graphics.CopyFromScreen($bounds.Left, $bounds.Top, 0, 0, $bounds.Size)
    $bmp.Save('${tempFile.replace(/\\/g, '\\\\')}', [System.Drawing.Imaging.ImageFormat]::Png)
    $graphics.Dispose()
    $bmp.Dispose()
    `;

    console.log(`🪟 [WINDOWS-SCREENSHOT] 执行PowerShell截图命令...`);
    await execFileAsync('powershell', ['-Command', psScript]);

    if (fs.existsSync(tempFile)) {
      const buffer = await fs.promises.readFile(tempFile);
      console.log(`✅ [WINDOWS-SCREENSHOT] Windows局部截图成功，大小: ${buffer.length} bytes`);

      try {
        await fs.promises.unlink(tempFile);
      } catch (err) {
        console.warn('⚠️ [WINDOWS-SCREENSHOT] 清理临时文件失败:', err);
      }

      return buffer;
    } else {
      throw new Error('PowerShell局部截图文件未创建');
    }
  }

  /**
   * 🧹 清理资源
   */
  cleanup(): void {
    if (this.areaSelectionState.tipWindow) {
      this.areaSelectionState.tipWindow.close();
      this.areaSelectionState.tipWindow = null;
    }

    if (this.areaSelectionState.isSelecting) {
      this.cancelAreaSelection();
    }
  }
}
