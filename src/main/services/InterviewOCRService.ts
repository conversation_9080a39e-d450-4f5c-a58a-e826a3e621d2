import { LocalScreenshotAnalysisService } from './LocalOCRService';
import { ReverseAnalysisScreenshotService } from './ReverseAnalysisScreenshotService';
import { ReverseAnalysisService } from './ReverseAnalysisService';
import { GroqVisionService } from './GroqVisionService';
import { ZhipuVisionService } from './ZhipuVisionService';
import { ServiceConfigManager } from './ServiceConfigManager';
import { OCRMode } from './ServiceTypes';
import * as fs from 'fs';

/**
 * 🎯 面试场景OCR服务 - 专门为面试场景优化的截图识别服务
 */
export class InterviewOCRService {
  private localAnalysisService: LocalScreenshotAnalysisService;
  private screenshotService: ReverseAnalysisScreenshotService;
  private remoteAnalysisService: ReverseAnalysisService;
  private groqVisionService: GroqVisionService;
  private zhipuVisionService: ZhipuVisionService;
  private configManager: ServiceConfigManager;
  private isProcessing = false;

  constructor() {
    this.localAnalysisService = new LocalScreenshotAnalysisService();
    this.screenshotService = new ReverseAnalysisScreenshotService();
    this.remoteAnalysisService = new ReverseAnalysisService();
    this.groqVisionService = new GroqVisionService();
    this.zhipuVisionService = new ZhipuVisionService();
    this.configManager = ServiceConfigManager.getInstance();
    console.log('🎯 InterviewOCRService initialized with quad-mode support (Local OCR, Remote API, Groq Vision, Zhipu Vision)');
  }

  /**
   * 🎯 快速全屏截图识别 - 面试场景优化
   */
  async quickFullscreenAnalysis(): Promise<any> {
    if (this.isProcessing) {
      return {
        success: false,
        error: 'OCR analysis is already in progress',
        type: 'interview-fullscreen'
      };
    }

    this.isProcessing = true;
    
    try {
      console.log('🎯 Starting quick fullscreen analysis for interview...');

      // 1. 全屏截图
      this.screenshotService.setScreenshotMode('fullscreen');
      const screenshotPath = await this.screenshotService.takeScreenshot();

      // 2. 根据配置选择分析模式
      const result = await this.analyzeWithSelectedMode(screenshotPath);
      console.log('🎯 Analysis result:', result);

      // 3. 面试场景优化处理
      const interviewResult = await this.processForInterview(result);
      console.log('🎯 Interview processing result:', interviewResult);
      
      // 4. 清理临时文件
      try {
        await fs.promises.unlink(screenshotPath);
      } catch (cleanupError) {
        console.warn('⚠️ Failed to delete screenshot:', cleanupError);
      }
      
      return {
        ...interviewResult,
        type: 'interview-fullscreen',
        screenshotType: 'fullscreen'
      };
    } catch (error) {
      console.error('❌ Quick fullscreen analysis failed:', error);
      return {
        success: false,
        error: error.message,
        type: 'interview-fullscreen'
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 🎯 快速区域截图识别 - 面试场景优化
   */
  async quickAreaAnalysis(): Promise<any> {
    if (this.isProcessing) {
      return {
        success: false,
        error: 'OCR analysis is already in progress',
        type: 'interview-area'
      };
    }

    this.isProcessing = true;
    
    try {
      console.log('🎯 Starting quick area analysis for interview...');
      
      // 1. 区域截图
      this.screenshotService.setScreenshotMode('area');
      const screenshotPath = await this.screenshotService.takeScreenshot();
      
      // 2. 根据配置选择分析模式
      const result = await this.analyzeWithSelectedMode(screenshotPath);
      console.log('🎯 Area analysis result:', result);

      // 3. 面试场景优化处理
      const interviewResult = await this.processForInterview(result);
      console.log('🎯 Area interview processing result:', interviewResult);
      
      // 4. 清理临时文件
      try {
        await fs.promises.unlink(screenshotPath);
      } catch (cleanupError) {
        console.warn('⚠️ Failed to delete screenshot:', cleanupError);
      }
      
      return {
        ...interviewResult,
        type: 'interview-area',
        screenshotType: 'area'
      };
    } catch (error) {
      console.error('❌ Quick area analysis failed:', error);
      return {
        success: false,
        error: error.message,
        type: 'interview-area'
      };
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 🔄 根据配置选择分析模式
   */
  private async analyzeWithSelectedMode(screenshotPath: string): Promise<any> {
    try {
      const config = this.configManager.getConfig();
      const ocrMode = config.ocrMode || OCRMode.LOCAL_OCR;

      console.log(`🔄 Using OCR mode: ${ocrMode}`);

      if (ocrMode === OCRMode.REMOTE_API) {
        // 使用远程API模式
        console.log('🌐 Using remote OpenAI GPT-4V analysis...');

        // 配置reverse-analysis服务
        if (config.reverseAnalysis?.accountsFilePath) {
          this.remoteAnalysisService.setAccountsFilePath(config.reverseAnalysis.accountsFilePath);
        }

        const remoteResult = await this.remoteAnalysisService.analyzeScreenshot(screenshotPath);

        if (remoteResult.success) {
          console.log('🌐✅ Remote analysis successful');
          return remoteResult;
        } else {
          console.log('🌐⚠️ Remote analysis failed, falling back to local OCR:', remoteResult.error);
          // 降级到本地OCR
          return await this.localAnalysisService.analyzeScreenshot(screenshotPath);
        }
      } else if (ocrMode === OCRMode.GROQ_VISION) {
        // 使用Groq Vision模式
        console.log('🚀 Using Groq Llama 4 Scout vision analysis...');

        const groqResult = await this.groqVisionService.analyzeScreenshot(screenshotPath);

        if (groqResult.success) {
          console.log('🚀✅ Groq vision analysis successful');
          return groqResult;
        } else {
          console.log('🚀⚠️ Groq vision analysis failed, falling back to local OCR:', groqResult.error);
          // 降级到本地OCR
          return await this.localAnalysisService.analyzeScreenshot(screenshotPath);
        }
      } else if (ocrMode === OCRMode.ZHIPU_VISION) {
        // 使用智谱AI Vision模式
        console.log('🤖 Using Zhipu AI GLM-4V vision analysis...');

        const zhipuResult = await this.zhipuVisionService.analyzeScreenshot(screenshotPath);

        if (zhipuResult.success) {
          console.log('🤖✅ Zhipu AI vision analysis successful');
          return zhipuResult;
        } else {
          console.log('🤖⚠️ Zhipu AI vision analysis failed, falling back to local OCR:', zhipuResult.error);
          // 降级到本地OCR
          return await this.localAnalysisService.analyzeScreenshot(screenshotPath);
        }
      } else {
        // 使用本地OCR模式
        console.log('🔍 Using local OCR + AI analysis...');
        return await this.localAnalysisService.analyzeScreenshot(screenshotPath);
      }
    } catch (error) {
      console.error('🔄❌ Analysis mode selection failed:', error);
      // 出错时降级到本地OCR
      console.log('🔄 Falling back to local OCR due to error');
      return await this.localAnalysisService.analyzeScreenshot(screenshotPath);
    }
  }

  /**
   * 🎯 面试场景优化处理
   */
  private async processForInterview(result: any): Promise<any> {
    if (!result.success) {
      return result;
    }

    try {
      // 检查是否是远程API、Groq Vision或智谱AI Vision结果
      if (result.method === 'Remote OpenAI GPT-4V Analysis' ||
          result.method === 'Groq Llama 4 Scout Vision Analysis' ||
          result.method === 'Zhipu AI GLM-4V Vision Analysis') {
        // 远程API、Groq Vision或智谱AI Vision已经完成了面试分析，直接返回结果
        console.log(`🌐 ${result.method} result, skipping additional processing`);
        return {
          ...result,
          interviewAnalysis: {
            category: result.category,
            quickAnswer: result.quickAnswer,
            keyPoints: result.keyPoints,
            confidence: result.confidence
          }
        };
      }

      // 本地OCR结果需要进一步处理
      const extractedText = result.ocrText || '';

      // 面试场景特殊处理
      const interviewAnalysis = await this.analyzeForInterview(extractedText);

      return {
        ...result,
        interviewAnalysis,
        quickAnswer: interviewAnalysis.quickAnswer,
        keyPoints: interviewAnalysis.keyPoints,
        category: interviewAnalysis.category
      };
    } catch (error) {
      console.error('❌ Interview processing failed:', error);
      return result; // 返回原始结果
    }
  }

  /**
   * 🎯 面试场景分析
   */
  private async analyzeForInterview(text: string): Promise<any> {
    try {
      if (!text || text.trim().length === 0) {
        return {
          category: 'no-text',
          quickAnswer: '未检测到文字内容',
          keyPoints: ['请确保截图包含清晰的文字'],
          confidence: 0
        };
      }

      // 使用Groq进行面试场景分析
      const groqApiKey = process.env.GROQ_API_KEY || '********************************************************';
      
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${groqApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'llama-3.1-8b-instant',
          messages: [
            {
              role: 'system',
              content: `你是一个专业的面试助手。分析用户提供的面试题目或内容，提供：
1. 题目类型分类（技术题、行为题、算法题、系统设计等）
2. 简洁的回答要点（3-5个关键点）
3. 一个可以直接说出的简短回答（30-50字）

请用JSON格式回复：
{
  "category": "题目类型",
  "quickAnswer": "简短回答",
  "keyPoints": ["要点1", "要点2", "要点3"],
  "confidence": 0.8
}`
            },
            {
              role: 'user',
              content: `请分析这个面试相关内容：\n\n${text}`
            }
          ],
          max_tokens: 500,
          temperature: 0.3
        })
      });

      if (response.ok) {
        const result = await response.json();
        const content = result.choices[0]?.message?.content;
        
        try {
          // 尝试解析JSON响应
          const analysis = JSON.parse(content);
          return {
            category: analysis.category || 'general',
            quickAnswer: analysis.quickAnswer || '需要更多信息来回答',
            keyPoints: analysis.keyPoints || ['请提供更清晰的问题'],
            confidence: analysis.confidence || 0.5
          };
        } catch (parseError) {
          // 如果JSON解析失败，使用文本内容
          return {
            category: 'general',
            quickAnswer: content.substring(0, 100),
            keyPoints: [content],
            confidence: 0.6
          };
        }
      } else {
        throw new Error(`Groq API failed: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Interview analysis failed:', error);
      return {
        category: 'error',
        quickAnswer: '分析失败，请重试',
        keyPoints: ['OCR识别成功，但AI分析出错'],
        confidence: 0
      };
    }
  }

  /**
   * 🔍 检查是否正在处理
   */
  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }

  /**
   * 🎯 处理区域选择点击
   */
  handleAreaSelectionClick(x: number, y: number): boolean {
    return this.screenshotService.handleAreaSelectionClick(x, y);
  }

  /**
   * 🔍 检查是否正在进行区域选择
   */
  isAreaSelecting(): boolean {
    return this.screenshotService.isAreaSelecting();
  }

  /**
   * 📊 获取区域选择状态
   */
  getAreaSelectionState(): { isSelecting: boolean; hasStartPoint: boolean } {
    return this.screenshotService.getAreaSelectionState();
  }

  /**
   * 📊 获取OCR服务状态
   */
  async getOCRStatus(): Promise<{
    currentMode: OCRMode;
    localOCRAvailable: boolean;
    remoteAPIAvailable: boolean;
    groqVisionAvailable: boolean;
    zhipuVisionAvailable: boolean;
    remoteAPIStatus?: any;
    groqVisionStatus?: any;
    zhipuVisionStatus?: any;
  }> {
    const config = this.configManager.getConfig();
    const currentMode = config.ocrMode || OCRMode.LOCAL_OCR;

    // 检查远程API状态
    let remoteAPIStatus = null;
    let remoteAPIAvailable = false;

    try {
      remoteAPIStatus = await this.remoteAnalysisService.getServiceStatus();
      remoteAPIAvailable = remoteAPIStatus.available;
    } catch (error) {
      console.error('Failed to get remote API status:', error);
    }

    // 检查Groq Vision状态
    let groqVisionStatus = null;
    let groqVisionAvailable = false;

    try {
      groqVisionStatus = await this.groqVisionService.getServiceStatus();
      groqVisionAvailable = groqVisionStatus.available;
    } catch (error) {
      console.error('Failed to get Groq Vision status:', error);
    }

    // 检查智谱AI Vision状态
    let zhipuVisionStatus = null;
    let zhipuVisionAvailable = false;

    try {
      zhipuVisionStatus = await this.zhipuVisionService.getServiceStatus();
      zhipuVisionAvailable = zhipuVisionStatus.available;
    } catch (error) {
      console.error('Failed to get Zhipu Vision status:', error);
    }

    return {
      currentMode,
      localOCRAvailable: true, // 本地OCR总是可用
      remoteAPIAvailable,
      groqVisionAvailable,
      zhipuVisionAvailable,
      remoteAPIStatus,
      groqVisionStatus,
      zhipuVisionStatus
    };
  }

  /**
   * 🧹 清理资源
   */
  async cleanup(): Promise<void> {
    await this.localAnalysisService.cleanup();
    this.screenshotService.cleanup();
  }
}
