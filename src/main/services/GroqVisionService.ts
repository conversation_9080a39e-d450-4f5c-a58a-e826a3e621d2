import { promises as fs } from 'fs';
import { ServiceConfigManager } from './ServiceConfigManager';

/**
 * 🚀 Groq Vision 分析服务
 * 使用 Groq 的 Llama 4 Scout 视觉模型直接分析图片
 */

export interface GroqVisionResult {
  success: boolean;
  category?: string;
  quickAnswer?: string;
  keyPoints?: string[];
  confidence?: number;
  response?: string;
  detailedAnalysis?: string;
  method: string;
  error?: string;
}

export class GroqVisionService {
  private configManager: ServiceConfigManager;
  private requestTimeout = 30000; // 30秒超时

  constructor() {
    this.configManager = ServiceConfigManager.getInstance();
  }

  /**
   * 🖼️ 分析截图图片
   */
  async analyzeScreenshot(imagePath: string): Promise<GroqVisionResult> {
    try {
      console.log('🚀 Starting Groq vision analysis...');
      console.log('📁 Image path:', imagePath);

      // 1. 检查图片文件
      if (!await this.checkImageFile(imagePath)) {
        return {
          success: false,
          error: 'Image file not found or invalid',
          method: 'Groq Llama 4 Scout Vision Analysis'
        };
      }

      // 2. 转换图片为base64
      const imageBase64 = await this.convertImageToBase64(imagePath);
      if (!imageBase64) {
        return {
          success: false,
          error: 'Failed to convert image to base64',
          method: 'Groq Llama 4 Scout Vision Analysis'
        };
      }

      // 3. 调用Groq Vision API
      const result = await this.callGroqVisionAPI(imageBase64);
      if (result.success) {
        console.log('🚀✅ Groq vision analysis completed successfully');
        return {
          ...result,
          method: 'Groq Llama 4 Scout Vision Analysis'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Groq API call failed',
          method: 'Groq Llama 4 Scout Vision Analysis'
        };
      }

    } catch (error) {
      console.error('🚀❌ Groq vision analysis failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        method: 'Groq Llama 4 Scout Vision Analysis'
      };
    }
  }

  /**
   * 🔍 检查图片文件
   */
  private async checkImageFile(imagePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(imagePath);
      return stats.isFile() && stats.size > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 🔄 转换图片为base64
   */
  private async convertImageToBase64(imagePath: string): Promise<string | null> {
    try {
      const imageBuffer = await fs.readFile(imagePath);
      
      // 检查图片大小，Groq有4MB限制
      const maxSize = 4 * 1024 * 1024; // 4MB
      if (imageBuffer.length > maxSize) {
        console.warn('🚀⚠️ Image too large for Groq API, may need compression');
      }

      const base64 = imageBuffer.toString('base64');
      console.log(`🔄 Image converted to base64, size: ${Math.round(base64.length / 1024)}KB`);
      
      return base64;
    } catch (error) {
      console.error('🔄❌ Failed to convert image to base64:', error);
      return null;
    }
  }

  /**
   * 🤖 调用Groq Vision API
   */
  private async callGroqVisionAPI(imageBase64: string): Promise<GroqVisionResult> {
    try {
      const config = this.configManager.getConfig();
      const groqApiKey = config.separated?.ai?.config?.apiKey;

      if (!groqApiKey) {
        throw new Error('Groq API key not found in configuration');
      }

      const prompt = `你是一个专业的面试助手。请仔细分析这张截图中的具体面试题目，根据实际内容提供针对性的回答。

重要：这是技术岗面试准备，如果是编程相关题目，必须提供完整的代码实现！

请提供：
1. 题目类型分类（算法题、系统设计、技术概念、行为题等）
2. 核心解题思路（3-5个要点）
3. 简洁的口头回答（30-50字，适合面试时直接说出）
4. 详细解析，必须包含完整的Java代码实现（对于编程题）

请严格按照以下JSON格式返回，根据实际题目内容填写：
{
  "category": "算法题",
  "quickAnswer": "根据实际题目提供简洁回答",
  "keyPoints": ["关键点1", "关键点2", "关键点3"],
  "response": "详细的解题思路说明，包含完整的Java代码实现：\\n\\n\`\`\`java\\npublic class Solution {\\n    public int solve(参数) {\\n        // 具体实现代码\\n        return result;\\n    }\\n}\\n\`\`\`\\n\\n时间复杂度：O(n)\\n空间复杂度：O(1)",
  "confidence": 0.9
}

重要：
1. 请根据截图中的实际内容进行分析
2. 对于编程题，response字段必须包含完整的Java代码实现
3. 代码要用代码块包围，确保格式正确`;

      const requestBody = {
        model: 'meta-llama/llama-4-scout-17b-16e-instruct', // 使用最新的Llama 4 Scout视觉模型
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/png;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_completion_tokens: 1500,
        temperature: 0.3,
        response_format: { type: 'json_object' }
      };

      console.log(`🤖 Calling Groq Vision API with Llama 4 Scout`);
      console.log(`🔑 Using API key: ${groqApiKey.substring(0, 10)}...`);

      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${groqApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.requestTimeout)
      });

      console.log(`🚀 Groq API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`🚀❌ Groq API Error Response: ${errorText}`);
        throw new Error(`Groq API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      const content = result.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('No content in Groq API response');
      }

      console.log('🚀 Groq API response content:', content.substring(0, 200) + '...');

      // 解析JSON响应
      try {
        const analysis = JSON.parse(content);
        return {
          success: true,
          category: analysis.category || 'general',
          quickAnswer: analysis.quickAnswer || '需要更多信息来回答',
          keyPoints: analysis.keyPoints || ['请提供更清晰的问题'],
          confidence: analysis.confidence || 0.7,
          response: analysis.response || content,
          detailedAnalysis: analysis.response || content
        };
      } catch (parseError) {
        console.error('🚀❌ JSON parsing failed:', parseError);
        console.log('🚀 Raw content:', content);
        
        // 如果JSON解析失败，使用文本内容
        return {
          success: true,
          category: 'general',
          quickAnswer: content.substring(0, 100),
          keyPoints: [content],
          confidence: 0.6,
          response: content,
          detailedAnalysis: content
        };
      }

    } catch (error) {
      console.error('🚀❌ Groq Vision API call failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 📊 获取服务状态
   */
  async getServiceStatus(): Promise<{
    available: boolean;
    model: string;
    lastError?: string;
  }> {
    try {
      const config = this.configManager.getConfig();
      const groqApiKey = config.separated?.ai?.config?.apiKey;
      
      return {
        available: !!groqApiKey,
        model: 'meta-llama/llama-4-scout-17b-16e-instruct'
      };
    } catch (error) {
      return {
        available: false,
        model: 'meta-llama/llama-4-scout-17b-16e-instruct',
        lastError: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
