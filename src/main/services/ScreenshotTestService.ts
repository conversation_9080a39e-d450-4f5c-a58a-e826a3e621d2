import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'

import { ReverseAnalysisScreenshotService } from './ReverseAnalysisScreenshotService'
import { LocalScreenshotAnalysisService } from './LocalOCRService'

/**
 * 🧪 ScreenshotTestService – helper utility that orchestrates
 * Reverse-Analysis style screenshot capture together with the existing
 * Local OCR + AI analysis pipeline.  It is **only** consumed by
 * `src/main/index.ts` for a series of IPC testing handlers, so we keep the
 * public surface limited to those methods referenced there.
 *
 * NOTE:  This implementation purposefully avoids any network requests to
 * 3-rd-party services so that it can compile and run in offline / CI
 * environments.  If you previously relied on cloud-based vision models
 * (e.g. “reverse-analysis” or OpenAI Vision) you can extend the class with
 * your own methods without affecting the current build.
 */
export class ScreenshotTestService {
  private readonly screenshotDir: string
  private readonly reverseAnalysisScreenshot: ReverseAnalysisScreenshotService
  private readonly localAnalysisService: LocalScreenshotAnalysisService

  constructor () {
    // All temporary screenshots go to the system tmp dir so we do not clutter
    // the repository or end-user home folders.
    this.screenshotDir = path.join(os.tmpdir(), 'geek-assistant-screenshots')
    this.ensureScreenshotDir()

    this.reverseAnalysisScreenshot = new ReverseAnalysisScreenshotService()
    this.localAnalysisService = new LocalScreenshotAnalysisService()
  }

  /* ------------------------------------------------------------------ */
  /*  Public  API – methods that `src/main/index.ts` expects to exist   */
  /* ------------------------------------------------------------------ */

  /** Full-screen capture followed by local OCR + AI analysis. */
  async runFullTest (): Promise<any> {
    try {
      // Ensure we are in fullscreen mode
      this.reverseAnalysisScreenshot.setScreenshotMode('fullscreen')
      const screenshotPath = await this.reverseAnalysisScreenshot.takeScreenshot()
      const analysis = await this.localAnalysisService.analyzeScreenshot(screenshotPath)
      await this.safeDelete(screenshotPath)
      return {
        ...analysis,
        screenshotType: 'fullscreen'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error?.message ?? String(error),
        screenshotType: 'fullscreen'
      }
    }
  }

  /** Analyse an existing image file with the local OCR / AI pipeline. */
  async testImageFile (imagePath: string): Promise<any> {
    try {
      return await this.localAnalysisService.analyzeScreenshot(imagePath)
    } catch (error: any) {
      return { success: false, error: error?.message ?? String(error) }
    }
  }

  /** Area-selection screenshot test. */
  async testAreaScreenshot (): Promise<any> {
    try {
      this.reverseAnalysisScreenshot.setScreenshotMode('area')
      const screenshotPath = await this.reverseAnalysisScreenshot.takeScreenshot()
      const analysis = await this.localAnalysisService.analyzeScreenshot(screenshotPath)
      await this.safeDelete(screenshotPath)
      return {
        ...analysis,
        screenshotType: 'area'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error?.message ?? String(error),
        screenshotType: 'area'
      }
    }
  }

  /** Pure local OCR + AI analysis (fullscreen). */
  async testLocalOCRAnalysis (): Promise<any> {
    return this.runFullTest()
  }

  /** Local OCR + AI analysis for an area-selection screenshot. */
  async testLocalOCRAreaAnalysis (): Promise<any> {
    try {
      this.reverseAnalysisScreenshot.setScreenshotMode('area')
      const screenshotPath = await this.reverseAnalysisScreenshot.takeScreenshot()
      const analysis = await this.localAnalysisService.analyzeScreenshot(screenshotPath)
      await this.safeDelete(screenshotPath)
      return {
        ...analysis,
        screenshotType: 'area',
        testType: 'local-ocr'
      }
    } catch (error: any) {
      return {
        success: false,
        error: error?.message ?? String(error),
        screenshotType: 'area',
        testType: 'local-ocr'
      }
    }
  }

  /** Proxy helper so global mouse listener can forward clicks. */
  handleAreaSelectionClick (x: number, y: number): boolean {
    return this.reverseAnalysisScreenshot.handleAreaSelectionClick(x, y)
  }

  /** True while the user is selecting an area screenshot. */
  isAreaSelecting (): boolean {
    return this.reverseAnalysisScreenshot.isAreaSelecting()
  }

  /** Retrieve internal selection state for debugging. */
  getAreaSelectionState (): any {
    return this.reverseAnalysisScreenshot.getAreaSelectionState()
  }

  /* ------------------------------------------------------------------ */
  /*                             Helpers                                */
  /* ------------------------------------------------------------------ */

  private ensureScreenshotDir (): void {
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true })
    }
  }

  private async safeDelete (filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath)
    } catch (_) {
      /* ignore */
    }
  }
}
