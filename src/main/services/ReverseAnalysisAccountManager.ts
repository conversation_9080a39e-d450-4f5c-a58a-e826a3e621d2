import { promises as fs } from 'fs';
import path from 'path';

/**
 * 🔐 Reverse Analysis 账号管理器
 * 负责读取和管理 reverse-analysis 项目的账号信息
 */

export interface ReverseAnalysisAccount {
  id: string;
  name: string;
  email: string;
  password: string;
  voiceTime: number;
  openaiCredentials: {
    apiKey: string;
    baseURL: string;
    userType: string;
  };
  azureCredentials?: {
    token: string;
    region: string;
    type: string;
  };
  subscriptionType?: string;
  createdAt?: string;
  sessionFromCache?: boolean;
}

export interface AccountsData {
  accounts: ReverseAnalysisAccount[];
  summary: {
    totalAccounts: number;
    validAccounts: number;
    totalVoiceMinutes: number;
    totalVoiceHours: number;
  };
}

export class ReverseAnalysisAccountManager {
  private static instance: ReverseAnalysisAccountManager;
  private accounts: ReverseAnalysisAccount[] = [];
  private currentAccountIndex = 0;
  private accountsFilePath: string;
  private lastLoadTime = 0;
  private loadCacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  private constructor() {
    // 默认路径，用户可以通过配置修改
    this.accountsFilePath = path.join(process.cwd(), 'reverse-analysis', 'accounts.json');
  }

  static getInstance(): ReverseAnalysisAccountManager {
    if (!ReverseAnalysisAccountManager.instance) {
      ReverseAnalysisAccountManager.instance = new ReverseAnalysisAccountManager();
    }
    return ReverseAnalysisAccountManager.instance;
  }

  /**
   * 🔧 设置accounts.json文件路径
   */
  setAccountsFilePath(filePath: string): void {
    this.accountsFilePath = filePath;
    this.lastLoadTime = 0; // 重置缓存
    console.log('🔐 ReverseAnalysisAccountManager: Accounts file path set to:', filePath);
  }

  /**
   * 📖 加载账号数据
   */
  async loadAccounts(): Promise<boolean> {
    try {
      const now = Date.now();
      
      // 检查缓存是否有效
      if (this.accounts.length > 0 && (now - this.lastLoadTime) < this.loadCacheTimeout) {
        console.log('🔐 Using cached accounts data');
        return true;
      }

      console.log('🔐 Loading accounts from:', this.accountsFilePath);
      
      // 检查文件是否存在
      try {
        await fs.access(this.accountsFilePath);
      } catch (error) {
        console.error('🔐❌ Accounts file not found:', this.accountsFilePath);
        return false;
      }

      // 读取并解析文件
      const fileContent = await fs.readFile(this.accountsFilePath, 'utf-8');
      const accountsData: AccountsData = JSON.parse(fileContent);

      if (!accountsData.accounts || !Array.isArray(accountsData.accounts)) {
        console.error('🔐❌ Invalid accounts data format');
        return false;
      }

      // 过滤有效账号（必须有OpenAI凭据）
      this.accounts = accountsData.accounts.filter(account => 
        account.openaiCredentials && 
        account.openaiCredentials.apiKey && 
        account.openaiCredentials.baseURL
      );

      this.lastLoadTime = now;
      console.log(`🔐✅ Loaded ${this.accounts.length} valid accounts`);
      
      return this.accounts.length > 0;
    } catch (error) {
      console.error('🔐❌ Failed to load accounts:', error);
      return false;
    }
  }

  /**
   * 🎯 获取当前可用账号
   */
  async getCurrentAccount(): Promise<ReverseAnalysisAccount | null> {
    if (this.accounts.length === 0) {
      const loaded = await this.loadAccounts();
      if (!loaded) {
        return null;
      }
    }

    if (this.accounts.length === 0) {
      return null;
    }

    return this.accounts[this.currentAccountIndex];
  }

  /**
   * 🔄 切换到下一个账号
   */
  async switchToNextAccount(): Promise<ReverseAnalysisAccount | null> {
    if (this.accounts.length === 0) {
      const loaded = await this.loadAccounts();
      if (!loaded) {
        return null;
      }
    }

    if (this.accounts.length === 0) {
      return null;
    }

    this.currentAccountIndex = (this.currentAccountIndex + 1) % this.accounts.length;
    console.log(`🔄 Switched to account ${this.currentAccountIndex + 1}/${this.accounts.length}`);
    
    return this.accounts[this.currentAccountIndex];
  }

  /**
   * 📊 获取账号统计信息
   */
  async getAccountsStatus(): Promise<{
    totalAccounts: number;
    currentAccountIndex: number;
    currentAccount: string | null;
    accountsFilePath: string;
    fileExists: boolean;
  }> {
    await this.loadAccounts();
    
    let fileExists = false;
    try {
      await fs.access(this.accountsFilePath);
      fileExists = true;
    } catch (error) {
      // File doesn't exist
    }

    return {
      totalAccounts: this.accounts.length,
      currentAccountIndex: this.currentAccountIndex,
      currentAccount: this.accounts.length > 0 ? this.accounts[this.currentAccountIndex].email : null,
      accountsFilePath: this.accountsFilePath,
      fileExists
    };
  }

  /**
   * 🧪 测试账号API可用性
   */
  async testAccountAPI(account: ReverseAnalysisAccount): Promise<boolean> {
    try {
      console.log(`🧪 Testing API for account: ${account.email}`);
      console.log(`🧪 API endpoint: ${account.openaiCredentials.baseURL}/models`);
      console.log(`🧪 API key: ${account.openaiCredentials.apiKey.substring(0, 10)}...`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${account.openaiCredentials.baseURL}/models`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${account.openaiCredentials.apiKey}`,
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log(`🧪 API test response: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const data = await response.json();
        console.log(`🧪✅ Account ${account.email} API test passed, models available: ${data.data?.length || 0}`);
        return true;
      } else {
        const errorText = await response.text();
        console.log(`🧪❌ Account ${account.email} API test failed: ${response.status} - ${errorText}`);
        return false;
      }
    } catch (error) {
      console.error(`🧪❌ Account ${account.email} API test error:`, error);
      return false;
    }
  }

  /**
   * 🔍 获取所有账号列表（用于调试）
   */
  async getAllAccounts(): Promise<ReverseAnalysisAccount[]> {
    await this.loadAccounts();
    return [...this.accounts];
  }

  /**
   * 🔄 刷新账号信息（强制重新加载）
   */
  async refreshAccounts(): Promise<boolean> {
    console.log('🔄 Force refreshing accounts...');
    this.lastLoadTime = 0; // 重置缓存时间
    this.accounts = []; // 清空缓存
    return await this.loadAccounts();
  }

  /**
   * 🧪 测试所有账号并返回可用账号列表
   */
  async getValidAccounts(): Promise<ReverseAnalysisAccount[]> {
    await this.loadAccounts();
    const validAccounts: ReverseAnalysisAccount[] = [];

    console.log(`🧪 Testing ${this.accounts.length} accounts...`);

    for (const account of this.accounts) {
      const isValid = await this.testAccountAPI(account);
      if (isValid) {
        validAccounts.push(account);
      }
    }

    console.log(`🧪 Found ${validAccounts.length} valid accounts out of ${this.accounts.length}`);
    return validAccounts;
  }

  /**
   * 🧹 清理缓存
   */
  clearCache(): void {
    this.accounts = [];
    this.lastLoadTime = 0;
    this.currentAccountIndex = 0;
    console.log('🧹 Account cache cleared');
  }
}
