import { ServiceAdapter, AdapterStatus } from './ServiceAdapter';
import { ServiceConfig, TranscriptionProvider, AIProvider } from './ServiceTypes';
import { DeepgramTranscriptionService } from './DeepgramTranscriptionServiceNew';
import { GladiaTranscriptionService } from './GladiaTranscriptionService';
import { AssemblyAITranscriptionService } from './AssemblyAITranscriptionServiceNew';
import { OpenAITranscriptionService } from './OpenAITranscriptionServiceNew';
import { SpeechmaticsTranscriptionService } from './SpeechmaticsTranscriptionService';
import { AzureTranscriptionService } from './AzureTranscriptionServiceSDK';
import { GroqAIService } from './GroqAIService';
import { TranscriptionAdapterFactory, AudioRequirements } from './TranscriptionProviderAdapter';

/**
 * 分离式服务适配器
 * 使用独立的转录服务和AI服务
 */
export class SeparatedAdapter extends ServiceAdapter {
  private transcriptionService: any = null;
  private aiService: any = null;
  private isTranscriptionActive = false;
  private currentTranscription = '';
  private transcriptionBuffer: string[] = [];
  private lastTranscriptionTime = 0;
  private aiResponseTimeout = 15000; // 15秒超时
  private currentAudioRequirements: AudioRequirements | null = null;
  private customPrompt: string = '';

  // 问题完成检测相关
  private questionCompletionTimer: NodeJS.Timeout | null = null;
  private questionCompletionDelay = 3000; // 3秒静音后认为问题完成
  private accumulatedQuestion = ''; // 累积的完整问题

  constructor(config: ServiceConfig) {
    super(config);
    console.log('🔄 SeparatedAdapter created with config:', config.mode);
  }

  /**
   * 初始化分离式服务
   */
  async initialize(config?: ServiceConfig): Promise<boolean> {
    try {
      if (config) {
        this.config = config;
      }

      if (!this.config.separated) {
        throw new Error('Separated service config is required');
      }

      console.log('🔄 Initializing separated services...');
      this.updateStatus(AdapterStatus.CONNECTING);

      // 获取转录服务的音频要求
      const transcriptionProvider = this.config.separated.transcription.provider;
      const adapter = TranscriptionAdapterFactory.getAdapter(transcriptionProvider);
      this.currentAudioRequirements = adapter.getAudioRequirements();

      console.log('🔄 Audio requirements for', transcriptionProvider, ':', this.currentAudioRequirements);

      // 初始化转录服务
      const transcriptionSuccess = await this.initializeTranscriptionService();
      if (!transcriptionSuccess) {
        throw new Error('Failed to initialize transcription service');
      }

      // 初始化AI服务
      const aiSuccess = await this.initializeAIService();
      if (!aiSuccess) {
        throw new Error('Failed to initialize AI service');
      }

      this.isInitialized = true;
      this.updateStatus(AdapterStatus.CONNECTED);
      console.log('🔄✅ Separated services initialized successfully');
      
      return true;
    } catch (error) {
      console.error('🔄❌ Failed to initialize separated services:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 启动会话
   */
  async startSession(): Promise<boolean> {
    try {
      if (!this.isReady()) {
        console.warn('🔄 Services not ready, attempting to initialize...');
        const success = await this.initialize();
        if (!success) {
          return false;
        }
      }

      console.log('🔄 Starting separated session...');

      // 启动转录服务
      if (this.transcriptionService) {
        const transcriptionStarted = await this.transcriptionService.startTranscription();
        if (transcriptionStarted) {
          this.isTranscriptionActive = true;
          console.log('🔄✅ Transcription service started');
        } else {
          throw new Error('Failed to start transcription service');
        }
      }

      console.log('🔄✅ Separated session started successfully');
      this.emitServiceEvent('session-started', { mode: 'separated' });
      
      return true;
    } catch (error) {
      console.error('🔄❌ Failed to start separated session:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 停止会话
   */
  async stopSession(): Promise<void> {
    try {
      console.log('🔄 Stopping separated session...');

      if (this.transcriptionService && this.isTranscriptionActive) {
        await this.transcriptionService.stopTranscription();
        this.isTranscriptionActive = false;
        console.log('🔄 Transcription service stopped');
      }

      this.currentTranscription = '';
      this.transcriptionBuffer = [];
      this.accumulatedQuestion = '';

      // 清理问题完成检测定时器
      if (this.questionCompletionTimer) {
        clearTimeout(this.questionCompletionTimer);
        this.questionCompletionTimer = null;
      }

      console.log('🔄✅ Separated session stopped');
      this.emitServiceEvent('session-stopped', { mode: 'separated' });
    } catch (error) {
      console.error('🔄❌ Failed to stop separated session:', error);
    }
  }

  /**
   * 发送音频数据
   */
  async sendAudio(audioData: Buffer): Promise<void> {
    if (this.transcriptionService && this.isTranscriptionActive) {
      try {
        await this.transcriptionService.sendAudio(audioData);
      } catch (error) {
        console.error('🔄❌ Failed to send audio to transcription service:', error);
      }
    }
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<boolean> {
    try {
      console.log('🔄 Reconnecting separated services...');
      
      await this.stopSession();
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const success = await this.initialize();
      if (success) {
        await this.startSession();
        console.log('🔄✅ Separated services reconnected successfully');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('🔄❌ Failed to reconnect separated services:', error);
      this.handleError(error);
      return false;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      console.log('🔄 Disconnecting separated services...');
      
      await this.stopSession();
      
      if (this.transcriptionService) {
        this.transcriptionService.cleanup();
        this.transcriptionService = null;
      }

      if (this.aiService) {
        this.aiService.cleanup();
        this.aiService = null;
      }

      this.updateStatus(AdapterStatus.DISCONNECTED);
      console.log('🔄✅ Separated services disconnected');
    } catch (error) {
      console.error('🔄❌ Failed to disconnect separated services:', error);
    }
  }

  /**
   * 更新转录语言
   */
  async updateTranscriptionLanguage(language: string): Promise<void> {
    console.log('🌍 SeparatedAdapter: Updating transcription language to:', language);

    // 更新配置
    this.config.transcription.language = language;
    if (this.config.separated?.transcription?.config) {
      this.config.separated.transcription.config.language = language;
    }

    // 如果转录服务正在运行，需要重新启动以应用新语言
    if (this.transcriptionService && this.isTranscriptionActive) {
      console.log('🌍 Restarting transcription service with new language...');

      try {
        // 停止当前转录
        await this.transcriptionService.stopTranscription();

        // 重新初始化转录服务
        await this.initializeTranscriptionService();

        // 重新开始转录
        await this.transcriptionService.startTranscription();

        console.log('🌍✅ Transcription service restarted with new language');
      } catch (error) {
        console.error('🌍❌ Failed to restart transcription service:', error);
        throw error;
      }
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(config: ServiceConfig): Promise<boolean> {
    try {
      console.log('🔄 Updating separated service config...');

      const oldConfig = this.config;
      this.config = config;

      // 检查是否需要重新初始化服务
      const needsReinit = this.checkIfReinitNeeded(oldConfig, config);

      if (needsReinit) {
        console.log('🔄 Config changes require reinitialization');
        await this.disconnect();
        await this.initialize();

        if (this.isTranscriptionActive) {
          await this.startSession();
        }
      }

      console.log('🔄✅ Separated service config updated');
      return true;
    } catch (error) {
      console.error('🔄❌ Failed to update separated service config:', error);
      return false;
    }
  }

  /**
   * 初始化转录服务
   */
  private async initializeTranscriptionService(): Promise<boolean> {
    try {
      const transcriptionConfig = this.config.separated!.transcription;
      const adapter = TranscriptionAdapterFactory.getAdapter(transcriptionConfig.provider);

      console.log('🔄 Initializing transcription service:', transcriptionConfig.provider);

      // 使用适配器获取服务商特定的配置
      const providerConfig = adapter.getProviderSpecificConfig({
        ...transcriptionConfig.config,
        language: transcriptionConfig.config.language || 'zh-CN'
      });

      console.log('🔄 Provider-specific config:', providerConfig);

      switch (transcriptionConfig.provider) {
        case TranscriptionProvider.DEEPGRAM:
          this.transcriptionService = new DeepgramTranscriptionService(providerConfig);
          break;

        case TranscriptionProvider.GLADIA:
          this.transcriptionService = new GladiaTranscriptionService(providerConfig);
          break;

        case TranscriptionProvider.ASSEMBLYAI:
          this.transcriptionService = new AssemblyAITranscriptionService({
            ...providerConfig,
            sampleRate: this.currentAudioRequirements?.sampleRate || 16000
          });
          break;

        case TranscriptionProvider.OPENAI:
          this.transcriptionService = new OpenAITranscriptionService(providerConfig);
          break;

        case TranscriptionProvider.SPEECHMATICS:
          this.transcriptionService = new SpeechmaticsTranscriptionService(providerConfig);
          break;

        case TranscriptionProvider.AZURE:
          this.transcriptionService = new AzureTranscriptionService(providerConfig);
          break;

        default:
          throw new Error(`Unsupported transcription provider: ${transcriptionConfig.provider}`);
      }

      // 设置事件监听
      this.setupTranscriptionEvents();

      // 初始化服务
      const success = await this.transcriptionService.initialize();
      
      if (success) {
        console.log('🔄✅ Transcription service initialized:', transcriptionConfig.provider);
      }
      
      return success;
    } catch (error) {
      console.error('🔄❌ Failed to initialize transcription service:', error);
      return false;
    }
  }

  /**
   * 初始化AI服务
   */
  private async initializeAIService(): Promise<boolean> {
    try {
      const aiConfig = this.config.separated!.ai;
      
      console.log('🔄 Initializing AI service:', aiConfig.provider);

      switch (aiConfig.provider) {
        case AIProvider.GROQ:
          this.aiService = new GroqAIService({
            apiKey: aiConfig.config.apiKey,
            model: aiConfig.config.model || 'llama-3.1-8b-instant',
            maxTokens: aiConfig.config.maxTokens || 500,
            temperature: aiConfig.config.temperature || 0.7
          });
          break;
        
        // 可以添加其他AI服务
        default:
          throw new Error(`Unsupported AI provider: ${aiConfig.provider}`);
      }

      // 设置事件监听
      this.setupAIEvents();

      // 初始化服务
      const success = await this.aiService.initialize();
      
      if (success) {
        console.log('🔄✅ AI service initialized:', aiConfig.provider);
      }
      
      return success;
    } catch (error) {
      console.error('🔄❌ Failed to initialize AI service:', error);
      return false;
    }
  }

  /**
   * 设置转录服务事件监听
   */
  private setupTranscriptionEvents(): void {
    if (!this.transcriptionService) return;

    this.transcriptionService.on('transcription', (result: any) => {
      this.handleTranscriptionResult(result);
    });

    this.transcriptionService.on('error', (error: Error) => {
      console.error('🔄❌ Transcription service error:', error);
      this.handleError(error);
    });

    this.transcriptionService.on('statusChange', (status: string) => {
      console.log('🔄 Transcription status:', status);
      this.emitServiceEvent('transcription-status-changed', status);
    });
  }

  /**
   * 设置AI服务事件监听
   */
  private setupAIEvents(): void {
    if (!this.aiService) return;

    this.aiService.on('response', (response: any) => {
      this.handleAIResponse(response);
    });

    this.aiService.on('error', (error: Error) => {
      console.error('🔄❌ AI service error:', error);
      this.handleError(error);
    });
  }

  /**
   * 处理转录结果
   */
  private handleTranscriptionResult(result: any): void {
    try {
      const { text, isFinal } = result;

      if (!text || !text.trim()) {
        return;
      }

      this.lastTranscriptionTime = Date.now();

      if (isFinal) {
        // 最终转录结果
        this.currentTranscription = text;
        this.transcriptionBuffer.push(text);

        console.log('🔄 Final transcription:', text);
        this.handleTranscription(result);

        // 启动问题完成检测定时器，而不是立即调用AI
        this.startQuestionCompletionTimer();
      } else {
        // 临时转录结果
        console.log('🔄 Interim transcription:', text);
        this.handleTranscription({ ...result, interim: true });
      }
    } catch (error) {
      console.error('🔄❌ Error handling transcription result:', error);
    }
  }

  /**
   * 启动问题完成检测定时器
   */
  private startQuestionCompletionTimer(): void {
    // 清除之前的定时器
    if (this.questionCompletionTimer) {
      clearTimeout(this.questionCompletionTimer);
    }

    // 累积转录文本
    this.accumulatedQuestion = this.transcriptionBuffer.join(' ').trim();

    // 启动新的定时器
    this.questionCompletionTimer = setTimeout(() => {
      this.onQuestionCompleted();
    }, this.questionCompletionDelay);

    console.log(`🔄⏰ Question completion timer started (${this.questionCompletionDelay}ms)`);
  }

  /**
   * 问题完成时的处理
   */
  private onQuestionCompleted(): void {
    const completeQuestion = this.accumulatedQuestion;

    if (!completeQuestion || completeQuestion.length < 3) {
      console.log('🔄⚠️ Question too short, skipping AI response');
      return;
    }

    console.log('🔄✅ Question completed:', completeQuestion);

    // 清空缓冲区，准备下一个问题
    this.transcriptionBuffer = [];
    this.accumulatedQuestion = '';

    // 现在调用AI生成回复
    this.generateAIResponse(completeQuestion);
  }

  /**
   * 生成AI回复
   */
  private async generateAIResponse(question: string): Promise<void> {
    if (!this.aiService || !question.trim()) {
      return;
    }

    try {
      console.log('🔄 Generating AI response for:', question.substring(0, 50) + '...');
      
      // 使用自定义提示词作为上下文，如果没有则使用默认上下文
      const context = this.customPrompt || `
面试信息：
- 面试类型：通用技术面试
- 职位：通用技术职位
- 经验要求：不限
- 关键技能：基础技能、沟通能力、问题解决能力
- 重点准备：保持自信、清晰表达、展示技能

请根据以上信息提供针对性的面试建议和回答。
      `.trim();

      const response = await this.aiService.generateAnswer(question, { customPrompt: context });
      
      if (response) {
        console.log('🔄✅ AI response generated:', response.substring(0, 100) + '...');
        this.handleAIResponse({ text: response, question });
      }
    } catch (error) {
      console.error('🔄❌ Failed to generate AI response:', error);
      this.handleError(error);
    }
  }

  /**
   * 检查是否需要重新初始化
   */
  private checkIfReinitNeeded(oldConfig: ServiceConfig, newConfig: ServiceConfig): boolean {
    if (!oldConfig.separated || !newConfig.separated) {
      return true;
    }

    // 检查转录服务是否改变
    const oldTrans = oldConfig.separated.transcription;
    const newTrans = newConfig.separated.transcription;
    
    if (oldTrans.provider !== newTrans.provider || 
        oldTrans.config.apiKey !== newTrans.config.apiKey) {
      return true;
    }

    // 检查AI服务是否改变
    const oldAI = oldConfig.separated.ai;
    const newAI = newConfig.separated.ai;
    
    if (oldAI.provider !== newAI.provider || 
        oldAI.config.apiKey !== newAI.config.apiKey) {
      return true;
    }

    return false;
  }

  /**
   * 获取当前转录服务的音频要求
   */
  getAudioRequirements(): AudioRequirements | null {
    return this.currentAudioRequirements;
  }

  /**
   * 更新自定义提示词
   */
  async updateCustomPrompt(customPrompt: string): Promise<void> {
    console.log('🎯 SeparatedAdapter: Updating custom prompt');
    this.customPrompt = customPrompt;

    // 如果AI服务支持更新提示词，则通知它
    if (this.aiService && typeof this.aiService.updateCustomPrompt === 'function') {
      await this.aiService.updateCustomPrompt(customPrompt);
    }
  }
}
