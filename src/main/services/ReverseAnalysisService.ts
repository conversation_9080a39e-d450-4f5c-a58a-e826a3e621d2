import { promises as fs } from 'fs';
import { ReverseAnalysisAccountManager, ReverseAnalysisAccount } from './ReverseAnalysisAccountManager';

/**
 * 🌐 Reverse Analysis 远程图片分析服务
 * 使用 reverse-analysis 项目的 OpenAI API 密钥直接分析图片
 */

export interface RemoteAnalysisResult {
  success: boolean;
  category?: string;
  quickAnswer?: string;
  keyPoints?: string[];
  confidence?: number;
  detailedAnalysis?: string;
  method: string;
  accountUsed?: string;
  error?: string;
}

export class ReverseAnalysisService {
  private accountManager: ReverseAnalysisAccountManager;
  private maxRetries = 3;
  private requestTimeout = 30000; // 30秒超时

  constructor() {
    this.accountManager = ReverseAnalysisAccountManager.getInstance();
  }

  /**
   * 🖼️ 分析截图图片
   */
  async analyzeScreenshot(imagePath: string): Promise<RemoteAnalysisResult> {
    try {
      console.log('🌐 Starting remote screenshot analysis...');
      console.log('📁 Image path:', imagePath);

      // 1. 检查图片文件
      if (!await this.checkImageFile(imagePath)) {
        return {
          success: false,
          error: 'Image file not found or invalid',
          method: 'Remote OpenAI GPT-4V Analysis'
        };
      }

      // 2. 转换图片为base64
      const imageBase64 = await this.convertImageToBase64(imagePath);
      if (!imageBase64) {
        return {
          success: false,
          error: 'Failed to convert image to base64',
          method: 'Remote OpenAI GPT-4V Analysis'
        };
      }

      // 3. 尝试使用不同账号进行分析
      let lastError: string = '';
      for (let attempt = 0; attempt < this.maxRetries; attempt++) {
        const account = attempt === 0 
          ? await this.accountManager.getCurrentAccount()
          : await this.accountManager.switchToNextAccount();

        if (!account) {
          lastError = 'No available accounts';
          continue;
        }

        console.log(`🌐 Attempt ${attempt + 1}: Using account ${account.email}`);

        try {
          // 先测试账号API可用性
          const isAccountValid = await this.accountManager.testAccountAPI(account);
          if (!isAccountValid) {
            lastError = `Account ${account.email} API test failed`;
            console.log(`🌐⚠️ ${lastError}`);
            continue;
          }

          const result = await this.callOpenAIVisionAPI(account, imageBase64);
          if (result.success) {
            console.log('🌐✅ Remote analysis completed successfully');
            return {
              ...result,
              method: 'Remote OpenAI GPT-4V Analysis',
              accountUsed: account.email
            };
          } else {
            lastError = result.error || 'API call failed';
            console.log(`🌐⚠️ Account ${account.email} failed: ${lastError}`);
          }
        } catch (error) {
          lastError = error instanceof Error ? error.message : String(error);
          console.log(`🌐❌ Account ${account.email} error: ${lastError}`);
        }
      }

      return {
        success: false,
        error: `All accounts failed. Last error: ${lastError}`,
        method: 'Remote OpenAI GPT-4V Analysis'
      };

    } catch (error) {
      console.error('🌐❌ Remote analysis failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        method: 'Remote OpenAI GPT-4V Analysis'
      };
    }
  }

  /**
   * 🔍 检查图片文件
   */
  private async checkImageFile(imagePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(imagePath);
      return stats.isFile() && stats.size > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 🔄 转换图片为base64
   */
  private async convertImageToBase64(imagePath: string): Promise<string | null> {
    try {
      const imageBuffer = await fs.readFile(imagePath);
      
      // 检查图片大小，OpenAI有20MB限制
      const maxSize = 20 * 1024 * 1024; // 20MB
      if (imageBuffer.length > maxSize) {
        console.warn('🌐⚠️ Image too large, may need compression');
      }

      const base64 = imageBuffer.toString('base64');
      console.log(`🔄 Image converted to base64, size: ${Math.round(base64.length / 1024)}KB`);
      
      return base64;
    } catch (error) {
      console.error('🔄❌ Failed to convert image to base64:', error);
      return null;
    }
  }

  /**
   * 🤖 调用OpenAI Vision API
   */
  private async callOpenAIVisionAPI(account: ReverseAnalysisAccount, imageBase64: string): Promise<RemoteAnalysisResult> {
    try {
      const prompt = `你是一个专业的面试助手。请仔细分析这张截图中的具体面试题目，根据实际内容提供针对性的回答。

重要：这是技术岗面试准备，如果是编程相关题目，必须提供完整的代码实现！

请提供：
1. 题目类型分类（算法题、系统设计、技术概念、行为题等）
2. 核心解题思路（3-5个要点）
3. 简洁的口头回答（30-50字，适合面试时直接说出）
4. 详细解析，必须包含完整的Java代码实现（对于编程题）

请严格按照以下JSON格式返回，根据实际题目内容填写：
{
  "category": "算法题",
  "quickAnswer": "根据实际题目提供简洁回答",
  "keyPoints": ["关键点1", "关键点2", "关键点3"],
  "response": "详细的解题思路说明，包含完整的Java代码实现：\\n\\n\`\`\`java\\npublic class Solution {\\n    public int solve(参数) {\\n        // 具体实现代码\\n        return result;\\n    }\\n}\\n\`\`\`\\n\\n时间复杂度：O(n)\\n空间复杂度：O(1)",
  "confidence": 0.9
}

重要：
1. 请根据截图中的实际内容进行分析
2. 对于编程题，response字段必须包含完整的Java代码实现
3. 代码要用代码块包围，确保格式正确`;

      const requestBody = {
        model: 'gpt-4o', // 使用最新的视觉模型
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: prompt
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/png;base64,${imageBase64}`,
                  detail: 'high'
                }
              }
            ]
          }
        ],
        max_tokens: 1500,
        temperature: 0.3
      };

      console.log(`🤖 Calling OpenAI API: ${account.openaiCredentials.baseURL}/chat/completions`);
      console.log(`🔑 Using API key: ${account.openaiCredentials.apiKey.substring(0, 10)}...`);
      console.log(`👤 Account: ${account.email} (${account.openaiCredentials.userType})`);

      const response = await fetch(`${account.openaiCredentials.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${account.openaiCredentials.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.requestTimeout)
      });

      console.log(`🌐 API Response: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`🌐❌ API Error Response: ${errorText}`);
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      const content = result.choices?.[0]?.message?.content;

      if (!content) {
        throw new Error('No content in API response');
      }

      // 尝试解析JSON响应
      try {
        // 清理JSON标记
        let cleanContent = content.trim();

        // 移除```json和```标记
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        // 处理可能的JSON截断问题
        if (!cleanContent.endsWith('}')) {
          console.log('🔧 JSON appears to be truncated, attempting to fix...');
          // 尝试找到最后一个完整的字段
          const lastCommaIndex = cleanContent.lastIndexOf(',');
          const lastBraceIndex = cleanContent.lastIndexOf('}');

          if (lastCommaIndex > lastBraceIndex) {
            // 移除最后一个不完整的字段
            cleanContent = cleanContent.substring(0, lastCommaIndex) + '\n}';
            console.log('🔧 Fixed truncated JSON');
          } else if (!cleanContent.includes('}')) {
            // 如果完全没有结束括号，添加一个
            cleanContent += '\n}';
          }
        }

        console.log('🧹 Cleaned content for JSON parsing:', cleanContent.substring(0, 300) + '...');

        const analysis = JSON.parse(cleanContent);
        return {
          success: true,
          category: analysis.category || 'general',
          quickAnswer: analysis.quickAnswer || '需要更多信息来回答',
          keyPoints: analysis.keyPoints || ['请提供更清晰的问题'],
          confidence: analysis.confidence || 0.7,
          response: analysis.response || analysis.detailedAnalysis || content,
          detailedAnalysis: analysis.response || analysis.detailedAnalysis || content
        };
      } catch (parseError) {
        console.error('🤖❌ JSON parsing failed:', parseError);
        console.log('🤖 Raw content:', content);

        // 如果JSON解析失败，使用文本内容
        return {
          success: true,
          category: 'general',
          quickAnswer: content.substring(0, 100),
          keyPoints: [content],
          confidence: 0.6,
          detailedAnalysis: content
        };
      }

    } catch (error) {
      console.error('🤖❌ OpenAI API call failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 📊 获取服务状态
   */
  async getServiceStatus(): Promise<{
    available: boolean;
    accountsStatus: any;
    lastError?: string;
  }> {
    try {
      const accountsStatus = await this.accountManager.getAccountsStatus();
      return {
        available: accountsStatus.totalAccounts > 0 && accountsStatus.fileExists,
        accountsStatus
      };
    } catch (error) {
      return {
        available: false,
        accountsStatus: null,
        lastError: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 🔧 设置accounts.json路径
   */
  setAccountsFilePath(filePath: string): void {
    this.accountManager.setAccountsFilePath(filePath);
  }

  /**
   * 🧪 诊断API连接问题
   */
  async diagnoseAPIIssues(): Promise<{
    totalAccounts: number;
    validAccounts: number;
    accountDetails: Array<{
      email: string;
      apiKey: string;
      baseURL: string;
      userType: string;
      isValid: boolean;
      error?: string;
    }>;
  }> {
    console.log('🧪 Starting API diagnosis...');

    const validAccounts = await this.accountManager.getValidAccounts();
    const allAccounts = await this.accountManager.getAllAccounts();

    const accountDetails = [];

    for (const account of allAccounts) {
      const isValid = validAccounts.includes(account);
      accountDetails.push({
        email: account.email,
        apiKey: account.openaiCredentials.apiKey.substring(0, 15) + '...',
        baseURL: account.openaiCredentials.baseURL,
        userType: account.openaiCredentials.userType,
        isValid
      });
    }

    const diagnosis = {
      totalAccounts: allAccounts.length,
      validAccounts: validAccounts.length,
      accountDetails
    };

    console.log('🧪 API Diagnosis Results:', diagnosis);
    return diagnosis;
  }
}
