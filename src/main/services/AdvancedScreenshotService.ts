import { BrowserWindow, screen, nativeImage, dialog } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * 🖼️ 高级截图服务 - 支持全屏和部分截图，参考reverse-analysis实现
 */
export class AdvancedScreenshotService {
  private screenshotDir: string;
  private screenshotMode: 'fullscreen' | 'area' = 'fullscreen';
  private selectionWindow: BrowserWindow | null = null;

  constructor() {
    this.screenshotDir = path.join(os.tmpdir(), 'geek-assistant-screenshots');
    this.ensureScreenshotDir();
  }

  /**
   * 确保截图目录存在
   */
  private ensureScreenshotDir(): void {
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
      console.log('📁 Screenshot directory created:', this.screenshotDir);
    }
  }

  /**
   * 🎯 设置截图模式
   */
  setScreenshotMode(mode: 'fullscreen' | 'area'): void {
    this.screenshotMode = mode;
    console.log(`📸 Screenshot mode set to: ${mode}`);
  }

  /**
   * 🖼️ 主要截图方法 - 根据模式选择全屏或部分截图
   */
  async takeScreenshot(): Promise<string> {
    try {
      console.log(`📸 Taking screenshot in mode: ${this.screenshotMode}`);
      
      if (this.screenshotMode === 'area') {
        return await this.takeAreaScreenshot();
      } else {
        return await this.takeFullScreenshot();
      }
    } catch (error) {
      console.error('❌ Failed to take screenshot:', error);
      throw error;
    }
  }

  /**
   * 🖼️ 全屏截图
   */
  private async takeFullScreenshot(): Promise<string> {
    try {
      const displays = screen.getAllDisplays();
      const primaryDisplay = displays[0];

      console.log('📸 Taking full screenshot of primary display:', primaryDisplay.bounds);

      // 使用desktopCapturer API
      const { desktopCapturer } = require('electron');
      
      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: {
          width: primaryDisplay.bounds.width,
          height: primaryDisplay.bounds.height
        }
      });

      if (sources.length === 0) {
        throw new Error('No screen sources available');
      }

      const screenshot = sources[0].thumbnail;
      return await this.saveScreenshot(screenshot, 'fullscreen');
    } catch (error) {
      console.error('❌ Failed to take full screenshot:', error);
      throw error;
    }
  }

  /**
   * 🎯 部分截图 - 参考reverse-analysis的区域选择实现
   */
  private async takeAreaScreenshot(): Promise<string> {
    try {
      console.log('🎯 Starting area screenshot...');
      
      // 1. 启动区域选择
      const selectedArea = await this.startAreaSelection();
      
      if (!selectedArea) {
        throw new Error('Screenshot selection canceled');
      }

      console.log('📐 Selected area:', selectedArea);

      // 2. 短暂延迟让选择窗口消失
      await new Promise(resolve => setTimeout(resolve, 200));

      // 3. 捕获指定区域
      return await this.captureAreaScreenshot(selectedArea);
    } catch (error) {
      console.error('❌ Failed to take area screenshot:', error);
      throw error;
    }
  }

  /**
   * 🎯 启动区域选择 - 创建透明覆盖窗口让用户选择区域
   */
  private async startAreaSelection(): Promise<{ x: number; y: number; width: number; height: number } | null> {
    return new Promise((resolve) => {
      try {
        const displays = screen.getAllDisplays();
        const primaryDisplay = displays[0];

        // 创建全屏透明选择窗口
        this.selectionWindow = new BrowserWindow({
          x: primaryDisplay.bounds.x,
          y: primaryDisplay.bounds.y,
          width: primaryDisplay.bounds.width,
          height: primaryDisplay.bounds.height,
          frame: false,
          transparent: true,
          alwaysOnTop: true,
          skipTaskbar: true,
          resizable: false,
          movable: false,
          webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
          }
        });

        // 加载区域选择HTML
        const selectionHTML = this.createSelectionHTML();
        this.selectionWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(selectionHTML)}`);

        // 监听区域选择结果
        this.selectionWindow.webContents.once('dom-ready', () => {
          this.selectionWindow?.webContents.executeJavaScript(`
            window.startSelection((result) => {
              require('electron').ipcRenderer.send('area-selected', result);
            });
          `);
        });

        // 处理选择结果
        const { ipcMain } = require('electron');
        const handleAreaSelected = (event: any, result: any) => {
          ipcMain.removeListener('area-selected', handleAreaSelected);
          
          if (this.selectionWindow) {
            this.selectionWindow.close();
            this.selectionWindow = null;
          }
          
          resolve(result);
        };

        ipcMain.on('area-selected', handleAreaSelected);

        // 处理窗口关闭
        this.selectionWindow.on('closed', () => {
          this.selectionWindow = null;
          resolve(null);
        });

      } catch (error) {
        console.error('❌ Failed to start area selection:', error);
        resolve(null);
      }
    });
  }

  /**
   * 🎨 创建区域选择的HTML界面
   */
  private createSelectionHTML(): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body {
            margin: 0;
            padding: 0;
            background: rgba(0, 0, 0, 0.3);
            cursor: crosshair;
            user-select: none;
            overflow: hidden;
          }
          .selection-box {
            position: absolute;
            border: 2px dashed #00ff00;
            background: rgba(0, 255, 0, 0.1);
            display: none;
          }
          .instruction {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 1000;
          }
        </style>
      </head>
      <body>
        <div class="instruction">拖拽选择截图区域，按ESC取消</div>
        <div class="selection-box" id="selectionBox"></div>
        
        <script>
          window.startSelection = function(callback) {
            let isSelecting = false;
            let startX, startY;
            const selectionBox = document.getElementById('selectionBox');
            
            document.addEventListener('mousedown', (e) => {
              isSelecting = true;
              startX = e.clientX;
              startY = e.clientY;
              selectionBox.style.left = startX + 'px';
              selectionBox.style.top = startY + 'px';
              selectionBox.style.width = '0px';
              selectionBox.style.height = '0px';
              selectionBox.style.display = 'block';
            });
            
            document.addEventListener('mousemove', (e) => {
              if (!isSelecting) return;
              
              const currentX = e.clientX;
              const currentY = e.clientY;
              const width = Math.abs(currentX - startX);
              const height = Math.abs(currentY - startY);
              const left = Math.min(startX, currentX);
              const top = Math.min(startY, currentY);
              
              selectionBox.style.left = left + 'px';
              selectionBox.style.top = top + 'px';
              selectionBox.style.width = width + 'px';
              selectionBox.style.height = height + 'px';
            });
            
            document.addEventListener('mouseup', (e) => {
              if (!isSelecting) return;
              
              const currentX = e.clientX;
              const currentY = e.clientY;
              const width = Math.abs(currentX - startX);
              const height = Math.abs(currentY - startY);
              const left = Math.min(startX, currentX);
              const top = Math.min(startY, currentY);
              
              if (width > 10 && height > 10) {
                callback({ x: left, y: top, width, height });
              } else {
                callback(null);
              }
            });
            
            document.addEventListener('keydown', (e) => {
              if (e.key === 'Escape') {
                callback(null);
              }
            });
          };
        </script>
      </body>
      </html>
    `;
  }

  /**
   * 🎯 捕获指定区域的截图
   */
  private async captureAreaScreenshot(area: { x: number; y: number; width: number; height: number }): Promise<string> {
    try {
      // 先获取全屏截图
      const displays = screen.getAllDisplays();
      const primaryDisplay = displays[0];

      const { desktopCapturer } = require('electron');
      const sources = await desktopCapturer.getSources({
        types: ['screen'],
        thumbnailSize: {
          width: primaryDisplay.bounds.width,
          height: primaryDisplay.bounds.height
        }
      });

      if (sources.length === 0) {
        throw new Error('No screen sources available');
      }

      const fullScreenshot = sources[0].thumbnail;
      
      // 裁剪指定区域
      const croppedImage = fullScreenshot.crop({
        x: area.x,
        y: area.y,
        width: area.width,
        height: area.height
      });

      return await this.saveScreenshot(croppedImage, 'area');
    } catch (error) {
      console.error('❌ Failed to capture area screenshot:', error);
      throw error;
    }
  }

  /**
   * 💾 保存截图
   */
  private async saveScreenshot(image: Electron.NativeImage, type: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `screenshot-${type}-${timestamp}.png`;
    const filepath = path.join(this.screenshotDir, filename);

    const buffer = image.toPNG();
    await fs.promises.writeFile(filepath, buffer);

    console.log(`📸 ${type} screenshot saved:`, filepath);
    console.log('📊 Screenshot size:', buffer.length, 'bytes');
    console.log('📊 Screenshot dimensions:', image.getSize());

    return filepath;
  }

  /**
   * 🧹 清理资源
   */
  cleanup(): void {
    if (this.selectionWindow) {
      this.selectionWindow.close();
      this.selectionWindow = null;
    }
  }
}
