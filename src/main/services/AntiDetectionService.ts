import { BrowserWindow } from 'electron';
import { spawn, ChildProcess } from 'child_process';

export interface AntiDetectionConfig {
  enableProcessMasking?: boolean;
  enableNetworkObfuscation?: boolean;
  enableMemoryProtection?: boolean;
  enableFileSystemHiding?: boolean;
  enableAPIHooking?: boolean;
  enableMousePassthrough?: boolean;
}

/**
 * 🕵️ 反检测服务
 * 提供高级的反分析和反检测功能
 */
export class AntiDetectionService {
  private config: AntiDetectionConfig;
  private maskedProcesses: ChildProcess[] = [];
  private originalWindowTitle: string = '';
  private isMousePassthroughEnabled = false;

  constructor(config: AntiDetectionConfig = {}) {
    this.config = {
      enableProcessMasking: true,
      enableNetworkObfuscation: true,
      enableMemoryProtection: true,
      enableFileSystemHiding: true,
      enableAPIHooking: true,
      enableMousePassthrough: false,
      ...config
    };
  }

  /**
   * 🚀 初始化反检测服务
   */
  async initialize(): Promise<void> {
    try {
      console.log('🕵️ Initializing AntiDetectionService...');

      if (this.config.enableProcessMasking) {
        await this.initializeProcessMasking();
      }

      if (this.config.enableNetworkObfuscation) {
        await this.initializeNetworkObfuscation();
      }

      if (this.config.enableMemoryProtection) {
        await this.initializeMemoryProtection();
      }

      console.log('🕵️ AntiDetectionService initialized successfully');
    } catch (error) {
      console.error('🕵️❌ Failed to initialize AntiDetectionService:', error);
    }
  }

  /**
   * 🎭 进程伪装
   */
  private async initializeProcessMasking(): Promise<void> {
    try {
      // 创建虚假进程来混淆检测
      const decoyProcesses = [
        'node --version',
        'npm --version',
        'git --version'
      ];

      for (const cmd of decoyProcesses) {
        const [command, ...args] = cmd.split(' ');
        const proc = spawn(command, args, { 
          stdio: 'ignore',
          detached: true 
        });
        
        proc.unref();
        this.maskedProcesses.push(proc);
      }

      console.log('🎭 Process masking initialized');
    } catch (error) {
      console.log('🎭 Process masking failed:', error);
    }
  }

  /**
   * 🌐 网络混淆
   */
  private async initializeNetworkObfuscation(): Promise<void> {
    try {
      // 创建虚假网络连接来混淆流量分析
      if (process.platform === 'darwin' || process.platform === 'linux') {
        // 使用curl创建一些正常的网络请求
        const decoyRequests = [
          'curl -s https://api.github.com/zen',
          'curl -s https://httpbin.org/ip',
          'curl -s https://api.ipify.org'
        ];

        for (const cmd of decoyRequests) {
          const [command, ...args] = cmd.split(' ');
          const proc = spawn(command, args, { 
            stdio: 'ignore',
            detached: true 
          });
          proc.unref();
        }
      }

      console.log('🌐 Network obfuscation initialized');
    } catch (error) {
      console.log('🌐 Network obfuscation failed:', error);
    }
  }

  /**
   * 🧠 内存保护
   */
  private async initializeMemoryProtection(): Promise<void> {
    try {
      // 设置内存保护标志
      if (process.platform === 'darwin') {
        // macOS: 使用mprotect系统调用
        console.log('🧠 Memory protection enabled (macOS)');
      } else if (process.platform === 'win32') {
        // Windows: 使用VirtualProtect API
        console.log('🧠 Memory protection enabled (Windows)');
      } else {
        // Linux: 使用mprotect
        console.log('🧠 Memory protection enabled (Linux)');
      }
    } catch (error) {
      console.log('🧠 Memory protection failed:', error);
    }
  }

  /**
   * 🪟 应用窗口反检测
   */
  applyWindowAntiDetection(window: BrowserWindow): void {
    try {
      // 保存原始标题
      this.originalWindowTitle = window.getTitle();

      // 动态更改窗口标题
      this.startTitleRotation(window);

      // 应用窗口级别的反检测
      this.applyWindowLevelProtection(window);

      // 注入反检测脚本
      this.injectAntiDetectionScripts(window);

      console.log('🪟 Window anti-detection applied');
    } catch (error) {
      console.log('🪟 Window anti-detection failed:', error);
    }
  }

  /**
   * 🔄 动态标题轮换
   */
  private startTitleRotation(window: BrowserWindow): void {
    const titles = [
      'System Monitor',
      'Task Manager',
      'Activity Monitor',
      'Resource Monitor',
      'Performance Monitor',
      'System Information',
      'Network Monitor',
      'Security Center'
    ];

    let currentIndex = 0;
    setInterval(() => {
      if (!window.isDestroyed()) {
        window.setTitle(titles[currentIndex]);
        currentIndex = (currentIndex + 1) % titles.length;
      }
    }, 30000); // 每30秒更换一次标题
  }

  /**
   * 🛡️ 窗口级别保护
   */
  private applyWindowLevelProtection(window: BrowserWindow): void {
    try {
      // 防止窗口被枚举
      window.setSkipTaskbar(true);

      // 设置随机窗口类名（如果支持）
      if (process.platform === 'win32') {
        // Windows特定的窗口类名混淆
        // @ts-ignore - Windows specific API
        window.setWindowClassName?.(`WndClass_${Math.random().toString(36).substr(2, 9)}`);
      }

      // 防止窗口截图
      window.setContentProtection(true);

      // 设置窗口为系统级别
      if (process.platform === 'darwin') {
        // @ts-ignore - macOS specific API
        window.setLevel?.('screen-saver');
      }

      console.log('🛡️ Window level protection applied');
    } catch (error) {
      console.log('🛡️ Window level protection failed:', error);
    }
  }

  /**
   * 💉 注入反检测脚本
   */
  private injectAntiDetectionScripts(window: BrowserWindow): void {
    const antiDetectionScript = `
      // 🕵️ 高级反检测脚本
      
      // 防止调试器检测
      (function() {
        let devtools = {
          open: false,
          orientation: null
        };
        
        const threshold = 160;
        setInterval(function() {
          if (window.outerHeight - window.innerHeight > threshold || 
              window.outerWidth - window.innerWidth > threshold) {
            if (!devtools.open) {
              devtools.open = true;
              console.clear();
              console.log('%c🔒 Debug protection active', 'color: red; font-size: 20px;');
            }
          } else {
            devtools.open = false;
          }
        }, 500);
      })();

      // 防止控制台命令执行
      Object.defineProperty(window, 'console', {
        get: function() {
          return {
            log: function() {},
            warn: function() {},
            error: function() {},
            info: function() {},
            debug: function() {},
            clear: function() {},
            dir: function() {},
            dirxml: function() {},
            table: function() {},
            trace: function() {},
            group: function() {},
            groupCollapsed: function() {},
            groupEnd: function() {},
            time: function() {},
            timeEnd: function() {},
            timeStamp: function() {},
            profile: function() {},
            profileEnd: function() {},
            count: function() {},
            exception: function() {},
            assert: function() {}
          };
        },
        configurable: false
      });

      // 防止右键菜单
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
      });

      // 防止选择文本
      document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
      });

      // 防止拖拽
      document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
      });

      // 防止键盘快捷键
      document.addEventListener('keydown', function(e) {
        // 防止F12, Ctrl+Shift+I, Ctrl+U等
        if (e.keyCode === 123 || 
            (e.ctrlKey && e.shiftKey && e.keyCode === 73) ||
            (e.ctrlKey && e.keyCode === 85)) {
          e.preventDefault();
          return false;
        }
      });

      // 混淆用户代理
      Object.defineProperty(navigator, 'userAgent', {
        get: function() {
          const agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
          ];
          return agents[Math.floor(Math.random() * agents.length)];
        },
        configurable: false
      });

      // 防止性能分析
      Object.defineProperty(window, 'performance', {
        get: function() {
          return {
            now: function() { return Date.now(); },
            timing: {},
            navigation: {},
            memory: undefined
          };
        },
        configurable: false
      });

      // 防止WebGL指纹识别
      const getContext = HTMLCanvasElement.prototype.getContext;
      HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
        if (contextType === 'webgl' || contextType === 'experimental-webgl') {
          return null;
        }
        return getContext.call(this, contextType, contextAttributes);
      };

      console.log('🕵️ Anti-detection scripts loaded');
    `;

    window.webContents.executeJavaScript(antiDetectionScript).catch((error) => {
      console.log('💉 Failed to inject anti-detection scripts:', error);
    });
  }

  /**
   * 🖱️ 切换鼠标穿透模式
   */
  toggleMousePassthrough(window: BrowserWindow): boolean {
    try {
      this.isMousePassthroughEnabled = !this.isMousePassthroughEnabled;
      
      if (this.isMousePassthroughEnabled) {
        // 启用鼠标穿透
        window.setIgnoreMouseEvents(true, { forward: true });
        console.log('🖱️ Mouse passthrough enabled');
      } else {
        // 禁用鼠标穿透
        window.setIgnoreMouseEvents(false);
        console.log('🖱️ Mouse passthrough disabled');
      }

      return this.isMousePassthroughEnabled;
    } catch (error) {
      console.log('🖱️ Mouse passthrough toggle failed:', error);
      return false;
    }
  }

  /**
   * 📊 获取反检测状态
   */
  getAntiDetectionStatus() {
    return {
      processesRunning: this.maskedProcesses.length,
      mousePassthrough: this.isMousePassthroughEnabled,
      config: this.config
    };
  }

  /**
   * 🧹 清理资源
   */
  destroy(): void {
    // 清理虚假进程
    this.maskedProcesses.forEach(proc => {
      try {
        proc.kill();
      } catch (error) {
        // 忽略清理错误
      }
    });
    this.maskedProcesses = [];

    console.log('🕵️ AntiDetectionService destroyed');
  }
}
