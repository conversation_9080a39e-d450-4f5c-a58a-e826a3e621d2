import * as fs from 'fs';
import * as path from 'path';
import * as https from 'https';
import { createWorker } from 'tesseract.js';
import { app } from 'electron';

/**
 * 🔍 本地OCR服务 - 使用Tesseract.js进行文字识别
 */
export class LocalOCRService {
  // Use static members to share a single worker across all instances (singleton pattern)
  private static worker: any = null;
  private static isInitialized = false;

  // Provide optional access to the shared worker
  public static getWorker() {
    return LocalOCRService.worker;
  }

  constructor() {
    console.log('🔍 LocalOCRService instance created');
    // 仅在第一次创建实例时进行预热
    if (!LocalOCRService.isInitialized) {
      // 提前预热 OCR worker，减少首次调用延迟（异步，不阻塞主流程）
      this.initialize()
        .then(() => console.log('🚀 OCR worker pre-warmed'))
        .catch(err => console.warn('⚠️ OCR worker pre-warm failed', err));
    }
  }

  /**
   * 🚀 初始化OCR工作器
   */
  async initialize(): Promise<void> {
    if (LocalOCRService.isInitialized) {
      return;
    }

    try {
      console.log('🔍 Initializing Tesseract.js worker...');
      // 使用 fast 模型 + SIMD 核心，加速 OCR
      const langCDN = 'https://tessdata.projectnaptha.com/4.0.0_fast';
      const coreCDN = 'https://cdn.jsdelivr.net/npm/tesseract.js-core@5.0.1/dist/tesseract-core-simd.wasm';
      LocalOCRService.worker = await createWorker('chi_sim+eng', 2, {
        langPath: langCDN,
        corePath: coreCDN,
        logger: (m) => {
          if (m.status === 'recognizing text') {
            console.log(`🔍 OCR Progress: ${Math.round(m.progress * 100)}%`);
          }
        }
      });
      
      // 确保模型文件本地缓存，避免后续网络请求
      await this.ensureLocalTrainedData(['chi_sim', 'eng'], langCDN);
      
      // 设置参数提升准确度：PSM 6 (单行) - 若 API 支持
      if (LocalOCRService.worker.setParameters) {
        await LocalOCRService.worker.setParameters({
          // 仅文本块，不做方向检测或可选功能
          tessedit_pageseg_mode: '6',
          load_system_dawg: '0',
          load_freq_dawg: '0',
          load_number_dawg: '0',
          load_punc_dawg: '0'
        });
      }
      
      console.log('✅ Tesseract.js worker initialized successfully');
      LocalOCRService.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize Tesseract.js worker:', error);
      throw error;
    }
  }

  /**
   * 📥 若本地无 traineddata.gz，则下载保存到 userData/tessdata
   */
  private async ensureLocalTrainedData(langs: string[], baseCDN: string) {
    try {
      const tessDir = path.join(app.getPath('userData'), 'tessdata');
      if (!fs.existsSync(tessDir)) fs.mkdirSync(tessDir, { recursive: true });

      const downloads: Promise<void>[] = [];
      for (const l of langs) {
        const file = path.join(tessDir, `${l}.traineddata.gz`);
        if (!fs.existsSync(file)) {
          const url = `${baseCDN}/${l}.traineddata.gz`;
          console.log('⬇️ Downloading traineddata:', url);
          downloads.push(new Promise((res, rej) => {
            const fileStream = fs.createWriteStream(file);
            https.get(url, (resp) => {
              resp.pipe(fileStream);
              fileStream.on('finish', () => fileStream.close(() => res()));
            }).on('error', (err) => {
              fs.unlinkSync(file);
              rej(err);
            });
          }));
        }
      }
      await Promise.all(downloads);
      if (downloads.length) console.log('✅ Traineddata cached locally');
      // 将 worker 的 langPath 指向本地缓存目录，后续识别无需网络
      if (downloads.length) {
        LocalOCRService.worker.setParameters && LocalOCRService.worker.setParameters({ langPath: tessDir });
      }
    } catch (err: unknown) {
      const message = err instanceof Error ? err.message : String(err);
      console.warn('⚠️ ensureLocalTrainedData failed:', message);
    }
  }

  /**
   * 🔍 从图片中提取文字
   */
  async extractTextFromImage(imagePath: string): Promise<string> {
    try {
      if (!LocalOCRService.isInitialized) {
        await this.initialize();
      }

      if (!fs.existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      console.log('🔍 Starting OCR text extraction...');
      console.log('📁 Image path:', imagePath);

      // ⏩ 预处理：使用 sharp 灰度 & 缩放以加速 OCR
      let processedPath = imagePath;
      try {
        const sharp = await import('sharp');
        const tmpPath = imagePath + '_pre.png';
        
        // 获取尺寸决定是否缩放
        let meta;
        try {
          const sharpMeta = await import('sharp');
          meta = await sharpMeta.default(imagePath).metadata();
        } catch {}
        
        const widthLimit = 1200; // 平衡速度与精度
        const needResize = meta && meta.width && meta.width > widthLimit;
        
        await sharp.default(imagePath)
          .resize(needResize ? { width: widthLimit, withoutEnlargement: true } : null)
          .grayscale()
          .sharpen()
          .normalise()
          .toFormat('png')
          .toFile(tmpPath);
        processedPath = tmpPath;
        console.log('⚡ Image preprocessed for OCR');
      } catch (preErr: unknown) {
        const message = preErr instanceof Error ? preErr.message : String(preErr);
        console.warn('⚠️ Image preprocessing skipped:', message);
      }

      const { data: { text } } = await LocalOCRService.worker.recognize(processedPath);
      
      console.log('✅ OCR text extraction completed');
      console.log('📊 Extracted text length:', text.length);
      console.log('📝 Extracted text preview:', text.substring(0, 200) + (text.length > 200 ? '...' : ''));

      // 删除临时文件
      if (processedPath !== imagePath) {
        fs.unlink(processedPath, () => {});
      }

      return text.trim();
    } catch (error) {
      console.error('❌ OCR text extraction failed:', error);
      throw error;
    }
  }

  /**
   * 🧹 清理资源
   */
  async cleanup(): Promise<void> {
    if (LocalOCRService.worker) {
      try {
        await LocalOCRService.worker.terminate();
        console.log('🧹 Tesseract.js worker terminated');
      } catch (error) {
        console.warn('⚠️ Failed to terminate Tesseract.js worker:', error);
      }
      LocalOCRService.worker = null;
      LocalOCRService.isInitialized = false;
    }
  }
}

/**
 * 🤖 本地AI分析服务 - 使用Groq进行文本分析
 */
export class LocalAIAnalysisService {
  private groqApiKey: string;

  constructor() {
    this.groqApiKey = process.env.GROQ_API_KEY || '********************************************************';
    console.log('🤖 LocalAIAnalysisService initialized');
  }

  /**
   * 🤖 分析提取的文字
   */
  async analyzeText(extractedText: string): Promise<any> {
    try {
      if (!extractedText || extractedText.trim().length === 0) {
        return {
          success: false,
          error: 'No text extracted from image'
        };
      }

      console.log('🤖 Starting AI text analysis...');
      console.log('📝 Text to analyze:', extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : ''));

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000);

      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.groqApiKey}`,
          'Content-Type': 'application/json'
        },
        signal: controller.signal,
        body: JSON.stringify({
          model: 'llama-3.1-8b-instant',
          messages: [
            {
              role: 'system',
              content: '你是一个专业的编程面试助手，使用中文回答。对于给定的编程题目，请按以下格式严格输出：\n\n思路:\n<概述解题思路>\n\n代码 (java):\n```java\n<完整、可运行的Java代码>\n```\n\n除了上述格式内容，不要输出任何多余文字或语言切换。'
            },
            {
              role: 'user',
              content: `题目内容如下：\n\n${extractedText}`
            }
          ],
          max_tokens: 1000,
          temperature: 0.3
        })
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ AI text analysis completed successfully');
        
        return {
          success: true,
          model: 'llama-3.1-8b-instant',
          provider: 'Groq (Local)',
          method: 'OCR + Text Analysis',
          extractedText: extractedText,
          response: result.choices[0]?.message?.content,
          usage: result.usage
        };
      } else {
        const error = await response.text();
        console.log('❌ Groq API failed:', response.status, error);
        
        return {
          success: false,
          error: `Groq API failed: ${response.status} ${error}`
        };
      }
    } catch (unknownError) {
      console.error('❌ AI text analysis failed:', unknownError);
      
      if (unknownError instanceof Error && unknownError.name === 'AbortError') {
        return {
          success: false,
          error: 'AI analysis timeout'
        };
      }
      
      if (unknownError instanceof Error) {
        return {
          success: false,
          error: unknownError.message
        };
      }
      
      return {
        success: false,
        error: String(unknownError)
      };
    }
  }
}

/**
 * 🔄 完整的本地截图分析服务
 */
export class LocalScreenshotAnalysisService {
  private ocrService: LocalOCRService;
  private aiService: LocalAIAnalysisService;

  constructor() {
    this.ocrService = new LocalOCRService();
    this.aiService = new LocalAIAnalysisService();
    console.log('🔄 LocalScreenshotAnalysisService initialized');
  }

  /**
   * 🔄 完整的截图分析流程：OCR + AI分析
   */
  async analyzeScreenshot(imagePath: string): Promise<any> {
    try {
      console.log('🔄 Starting complete local screenshot analysis...');
      
      // 1. OCR文字提取
      console.log('📝 Step 1: OCR text extraction...');
      const extractedText = await this.ocrService.extractTextFromImage(imagePath);
      
      if (!extractedText || extractedText.trim().length === 0) {
        return {
          success: false,
          error: 'No text could be extracted from the screenshot',
          method: 'Local OCR + AI Analysis'
        };
      }

      // 2. AI文本分析
      console.log('🤖 Step 2: AI text analysis...');
      const analysisResult = await this.aiService.analyzeText(extractedText);
      
      console.log('✅ Complete local screenshot analysis finished');
      
      return {
        ...analysisResult,
        method: 'Local OCR + AI Analysis',
        ocrText: extractedText
      };
    } catch (error: any) {
      console.error('❌ Complete local screenshot analysis failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        method: 'Local OCR + AI Analysis'
      };
    }
  }

  /**
   * 🧹 清理资源
   */
  async cleanup(): Promise<void> {
    await this.ocrService.cleanup();
  }
}
