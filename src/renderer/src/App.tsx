import React, { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import WelcomePage from './components/WelcomePage'
import AIConfigPage from './components/AIConfigPage'
import MainPage from './components/MainPage'
import CreatePreparationPage from './components/CreatePreparationPage'
import FloatingWindow from './components/FloatingWindow'
import CollaborationMode from './components/CollaborationMode'
import StealthStatusPanel from './components/StealthStatusPanel'

// 声明全局类型
declare global {
  interface Window {
    geekAssistant: {
      // 窗口管理
      createFloatingWindow: () => Promise<boolean>
      closeFloatingWindow: () => Promise<boolean>
      enterCollaborationMode: () => Promise<boolean>
      exitCollaborationMode: () => Promise<boolean>

      // 隐身模式控制
      getStealthStatus: () => Promise<any>
      toggleMainWindowStealth: () => Promise<boolean>
      toggleFloatingWindowStealth: () => Promise<boolean>
      toggleOverallVisibility: () => Promise<boolean>
      toggleSuperStealthMode: () => Promise<boolean>

      // 反检测功能
      getAntiDetectionStatus: () => Promise<any>
      toggleMousePassthrough: () => Promise<boolean>
      toggleFloatingMousePassthrough: () => Promise<boolean>

      // 性能和安全监控
      getPerformanceReport: () => Promise<any>
      getOptimizationSuggestions: () => Promise<string[]>
      getSecurityReport: () => Promise<any>
      getCompatibilityInfo: () => Promise<any>

      // 高级窗口隐藏
      applyReverseAnalysisStealth: () => Promise<boolean>
      restoreWindowVisibility: () => Promise<boolean>
      getHiddenWindowsStatus: () => Promise<{ count: number; windowIds: number[] }>

      // 全局事件保护
      getGlobalEventProtectionStatus: () => Promise<any>
      stopKeyboardProtection: () => Promise<boolean>
      stopMouseProtection: () => Promise<boolean>

      // reverse-analysis鼠标穿透控制
      enableClickThrough: () => Promise<{ success?: boolean; error?: string }>
      disableClickThrough: () => Promise<{ success?: boolean; error?: string }>

      // reverse-analysis屏幕共享保护
      enableContentProtection: () => Promise<{ success?: boolean; error?: string }>
      disableContentProtection: () => Promise<{ success?: boolean; error?: string }>
      getContentProtectionStatus: () => Promise<{ enabled: boolean; supported: boolean }>

      // 事件监听
      onMousePassthroughChanged: (callback: (enabled: boolean) => void) => () => void

      // 🧪 截图测试功能
      testScreenshotRecognition: () => Promise<any>
      testImageFile: (imagePath: string) => Promise<any>
      testAreaScreenshot: () => Promise<any>
      testLocalOCR: () => Promise<any>
      testLocalOCRArea: () => Promise<any>

      // 🎯 面试OCR功能
      interviewOCRFullscreen: () => Promise<any>
      interviewOCRArea: () => Promise<any>
      getOCRStatus: () => Promise<any>
      diagnoseRemoteAPI: () => Promise<any>
      onInterviewOCRResult: (callback: (data: any) => void) => void
      removeInterviewOCRListener: () => void
      onTestIPC: (callback: (data: any) => void) => void
      testIPCCommunication: () => Promise<any>
      
      // 服务管理
      initializeServices: () => Promise<boolean>
      switchService: (config: any) => Promise<boolean>
      startSession: () => Promise<boolean>
      stopSession: () => Promise<boolean>
      
      // 音频管理
      startAudioCapture: () => Promise<boolean>
      stopAudioCapture: () => Promise<boolean>
      
      // 配置管理
      getCurrentServiceConfig: () => Promise<any>
      updateServiceConfig: (config: any) => Promise<boolean>
      
      // 传统Gemini API（向后兼容）
      initializeGemini: (apiKey: string, customPrompt?: string, profile?: string, language?: string) => Promise<boolean>
      reconnectGemini: () => Promise<boolean>
      manualReconnect: () => Promise<boolean>
      disconnectGemini: () => Promise<boolean>
      
      // 权限管理
      checkPermissions: () => Promise<any>
      checkScreenRecordingPermission: () => Promise<any>
      checkMicrophonePermission: () => Promise<any>
      checkApiKeyStatus: () => Promise<any>
      checkAudioDeviceStatus: () => Promise<any>
      openSystemPreferences: (pane: string) => Promise<boolean>
      testAudioCapture: () => Promise<any>
      requestMicrophonePermission: () => Promise<any>
      
      // 事件监听
      onStatusUpdate: (callback: (status: string) => void) => () => void
      onTranscriptionUpdate: (callback: (text: string) => void) => () => void
      onAIResponse: (callback: (response: any) => void) => () => void
      onSessionError: (callback: (error: string) => void) => () => void
      onSessionClosed: (callback: () => void) => () => void
    }
  }
}

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'welcome' | 'config' | 'main'>('welcome')
  const [isConfigured, setIsConfigured] = useState(false)

  useEffect(() => {
    // 临时清除localStorage以便测试欢迎页
    localStorage.removeItem('geekassistant-has-seen-welcome')
    localStorage.removeItem('geekassistant-configured')

    // 检查是否已经配置过
    const hasSeenWelcome = localStorage.getItem('geekassistant-has-seen-welcome')
    const hasConfigured = localStorage.getItem('geekassistant-configured')

    if (hasSeenWelcome && hasConfigured) {
      setCurrentStep('main')
      setIsConfigured(true)
    } else if (hasSeenWelcome) {
      setCurrentStep('config')
    }
  }, [])

  const handleWelcomeComplete = () => {
    localStorage.setItem('geekassistant-has-seen-welcome', 'true')
    localStorage.setItem('geekassistant-configured', 'true')
    setIsConfigured(true)
    setCurrentStep('main')
  }

  const handleConfigComplete = () => {
    localStorage.setItem('geekassistant-configured', 'true')
    setIsConfigured(true)
    setCurrentStep('main')
  }

  const handleBackToConfig = () => {
    setCurrentStep('config')
  }

  return (
    <Router>
      <Routes>
        <Route 
          path="/" 
          element={
            currentStep === 'welcome' ? (
              <WelcomePage onComplete={handleWelcomeComplete} />
            ) : currentStep === 'config' ? (
              <AIConfigPage 
                onComplete={handleConfigComplete}
                onBack={() => setCurrentStep('welcome')}
              />
            ) : (
              <MainPage 
                onBackToConfig={handleBackToConfig}
                isConfigured={isConfigured}
              />
            )
          } 
        />
        <Route path="/create-preparation" element={<CreatePreparationPage />} />
        <Route path="/collaboration" element={<CollaborationMode />} />
        <Route path="/floating" element={<FloatingWindow />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>

      {/* 隐身状态监控面板 - 在所有页面显示 */}
      <StealthStatusPanel />
    </Router>
  )
}

export default App
