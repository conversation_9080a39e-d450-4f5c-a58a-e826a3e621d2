import React, { useState, useEffect, useRef } from 'react'

// Make TypeScript aware of the preload-exposed API in this file’s scope
declare const window: Window & typeof globalThis & {
  geekAssistant: any
}

import { X, Mic, RotateCcw, Trash2, <PERSON>, Co<PERSON> } from 'lucide-react'
import { getLanguageConfig } from '../utils/i18n'

interface CollaborationModeProps {
  onClose: () => void
  mode: 'interview' | 'meeting'
  isEmbedded?: boolean
  language?: string
}

const CollaborationMode: React.FC<CollaborationModeProps> = ({ onClose, mode, isEmbedded = false, language = 'zh' }) => {
  // 获取当前语言配置
  const t = getLanguageConfig(language)
  const [transcription, setTranscription] = useState('')
  const [aiResponse, setAiResponse] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [status, setStatus] = useState('准备中...')
  const [isGeminiConnected, setIsGeminiConnected] = useState(false)
  const [serviceConfig, setServiceConfig] = useState<any>(null)
  const [transcriptionHistory, setTranscriptionHistory] = useState<Array<{
    content: string
    timestamp: Date
  }>>([])
  const [aiResponses, setAiResponses] = useState<Array<{
    content: string
    timestamp: Date
  }>>([])

  const transcriptionRef = useRef<HTMLDivElement>(null)
  const aiResponseRef = useRef<HTMLDivElement>(null)

  // 已移除测试IPC监听器，避免生产环境弹窗干扰

  // 设置面试OCR监听器 - 分离OCR识别和AI回答
  useEffect(() => {
    const extractQuestion = (text: string): string => {
      if (!text) return ''
      const stopWords = ['示例', '输入', '输出', '样例', 'Example', 'Sample']
      const lines = text.split(/\r?\n/).map(l => l.trim()).filter(Boolean)
      const resultLines: string[] = []
      for (const line of lines) {
        if (stopWords.some(w => line.startsWith(w))) break
        resultLines.push(line)
      }
      return resultLines.join(' ')
    }

    const handleInterviewOCRResult = (data: any) => {
      console.log('🎯 React handler called with data:', data)
      const { type, result } = data

      if (result && result.success) {
        // 链路1：OCR识别结果 -> 转录区域
        const question = extractQuestion(result.ocrText || '')
        const ocrContent = `[${type === 'fullscreen' ? '全屏' : '区域'}] ${question}`
        console.log('🎯 Updating transcription with OCR result')
        setTranscription(ocrContent)

        // 链路2：AI分析回答 -> AI回答区域
        const quickAnswer = result.quickAnswer || result.interviewAnalysis?.quickAnswer || ''
        const keyPoints = result.keyPoints || result.interviewAnalysis?.keyPoints || []
        const detailed = result.response || ''

        // 提取代码片段：优先选择 java 代码块，否则取首个代码块
        let codeSnippet = ''
        const javaMatch = detailed.match(/```(?:java|Java)[\s\S]*?```/)
        const anyMatch = detailed.match(/```[\s\S]*?```/)
        const rawBlock = javaMatch || anyMatch
        if (rawBlock) {
          codeSnippet = rawBlock[0]
            .replace(/```(?:java|Java)?[\r\n]?/, '') // 去掉开头 ```java
            .replace(/```$/, '') // 去掉结尾 ```
            .trim()
        }

        const approach = quickAnswer || keyPoints.join('\n')
        const aiReply = `思路:\n${approach}\n\n代码 (java):\n\n${codeSnippet || '// 代码生成中...'}`

        console.log('🎯 Updating AI response with analysis result')
        setAiResponse(aiReply)

        console.log('🎯 Both OCR and AI results updated successfully')
      }
    }

    // 注册回调函数到preload
    console.log('🎯 Registering interview OCR callback to preload')
    window.geekAssistant.onInterviewOCRResult(handleInterviewOCRResult)

    return () => {
      console.log('🎯 Cleaning up interview OCR callback')
      window.geekAssistant.removeInterviewOCRListener()
    }
  }, [])

  useEffect(() => {
    const initializeSystem = async () => {
      try {
        console.log(`🪟 CollaborationMode: Initializing ${mode} mode...`)

        // 获取服务配置
        try {
          const config = await window.geekAssistant.getCurrentServiceConfig()
          if (config) {
            setServiceConfig(config)
            console.log('🔧 CollaborationMode: Service config loaded:', config)
          } else {
            console.warn('🔧 CollaborationMode: No config found, reloading...')
            // 尝试重新加载配置
            const reloadedConfig = await window.geekAssistant.reloadServiceConfig()
            if (reloadedConfig) {
              setServiceConfig(reloadedConfig)
              console.log('🔧 CollaborationMode: Config reloaded:', reloadedConfig)
            }
          }
        } catch (error) {
          console.error('🔧❌ CollaborationMode: Could not load service config:', error)
        }

        setStatus('正在连接服务...')

        // 读取选择的面试准备项并应用定制化提示词
        if (mode === 'interview') {
          try {
            const selectedPreparation = localStorage.getItem('geekassistant-selected-preparation')
            if (selectedPreparation) {
              const preparation = JSON.parse(selectedPreparation)
              console.log('🎯 CollaborationMode: 使用面试准备项:', preparation.title)

              // 将面试准备项信息传递给后端，用于生成定制化AI提示词
              await window.geekAssistant.setInterviewContext(preparation)
            }
          } catch (error) {
            console.warn('🎯 CollaborationMode: 无法读取面试准备项:', error)
          }
        }

        // 直接启动会话（这里会根据当前配置连接API）
        const sessionStarted = await window.geekAssistant.startSession()
        if (!sessionStarted) {
          setStatus('错误：无法连接到AI服务，请检查配置和网络')
          return
        }

        setStatus('正在启动音频捕获...')

        // 启动音频捕获
        const audioStarted = await window.geekAssistant.startAudioCapture()
        if (audioStarted) {
          setStatus(mode === 'interview' ? '面试助手准备就绪' : '会议助手准备就绪')
          setIsConnected(true)
          setIsGeminiConnected(true)
        } else {
          setStatus('错误：无法启动音频捕获。请检查系统音频权限')
        }
      } catch (error) {
        console.error(`🪟 CollaborationMode: ${mode} mode 初始化错误:`, error)
        const errMsg = error instanceof Error ? error.message : String(error)
        setStatus(`错误：${errMsg}`)
      }
    }

    const setupEventListeners = () => {
      const statusUnsubscribe = window.geekAssistant.onStatusUpdate((newStatus: string) => {
        setStatus(newStatus)
      })

      const transcriptionUnsubscribe = window.geekAssistant.onTranscriptionUpdate((text: string) => {
        console.log('🎙️ CollaborationMode: Received transcription:', text)
        if (text.trim()) {
          setTranscription(text)
          setTranscriptionHistory(prev => [
            ...prev,
            {
              content: text,
              timestamp: new Date()
            }
          ])
        }
      })

      const aiResponseUnsubscribe = window.geekAssistant.onAIResponse((response: any) => {
        const responseText = response.text || response
        if (responseText.trim()) {
          setAiResponse(responseText)
          setAiResponses(prev => [
            ...prev,
            {
              content: responseText,
              timestamp: new Date()
            }
          ])
        }
      })

      const errorUnsubscribe = window.geekAssistant.onSessionError((error: string) => {
        setStatus(`错误：${error}`)
        setIsConnected(false)
        setIsGeminiConnected(false)
      })

      const closedUnsubscribe = window.geekAssistant.onSessionClosed(() => {
        setStatus('会话已关闭')
        setIsConnected(false)
        setIsGeminiConnected(false)
      })

      // 监听配置更新
      const configUpdateUnsubscribe = window.geekAssistant.onConfigUpdated((config: any) => {
        console.log('🔧 CollaborationMode: Config updated:', config)
        setServiceConfig(config)
      })

      return () => {
        statusUnsubscribe()
        transcriptionUnsubscribe()
        aiResponseUnsubscribe()
        errorUnsubscribe()
        closedUnsubscribe()
        configUpdateUnsubscribe()
      }
    }

    initializeSystem()
    const cleanup = setupEventListeners()

    return () => {
      cleanup()
      window.geekAssistant.stopAudioCapture()
      window.geekAssistant.stopSession()
    }
  }, [mode])

  // 自动滚动
  useEffect(() => {
    if (transcriptionRef.current) {
      transcriptionRef.current.scrollTop = transcriptionRef.current.scrollHeight
    }
  }, [transcriptionHistory])

  useEffect(() => {
    if (aiResponseRef.current) {
      aiResponseRef.current.scrollTop = aiResponseRef.current.scrollHeight
    }
  }, [aiResponses])

  const handleReconnect = async () => {
    setStatus('正在重连...')
    try {
      // 先停止当前会话
      await window.geekAssistant.stopSession()
      await window.geekAssistant.stopAudioCapture()

      // 重新启动会话
      const sessionStarted = await window.geekAssistant.startSession()
      if (sessionStarted) {
        const audioStarted = await window.geekAssistant.startAudioCapture()
        if (audioStarted) {
          setIsGeminiConnected(true)
          setIsConnected(true)
          setStatus('重连成功')
        } else {
          setStatus('重连失败：无法启动音频捕获')
        }
      } else {
        setStatus('重连失败：无法连接到AI服务')
      }
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : String(error)
      setStatus(`重连错误：${errMsg}`)
    }
  }

  const handleClearHistory = () => {
    setTranscriptionHistory([])
    setAiResponses([])
    setTranscription('')
    setAiResponse('')
  }

  const getServiceDisplayName = () => {
    if (!serviceConfig) return '加载中...'

    if (serviceConfig.mode === 'gemini-live') {
      return 'Google Gemini Live'
    } else if (serviceConfig.mode === 'separated') {
      const transcription = serviceConfig.separated?.transcription?.provider || 'Unknown'
      const ai = serviceConfig.separated?.ai?.provider || 'Unknown'

      // 映射provider名称到显示名称
      const transcriptionName = transcription === 'assemblyai' ? 'AssemblyAI' :
                               transcription === 'deepgram' ? 'Deepgram' :
                               transcription === 'speechmatics' ? 'Speechmatics' :
                               transcription.charAt(0).toUpperCase() + transcription.slice(1)

      const aiName = ai === 'groq' ? 'Groq' :
                     ai === 'openai' ? 'OpenAI' :
                     ai.charAt(0).toUpperCase() + ai.slice(1)

      return `${transcriptionName} + ${aiName}`
    }
    return '未知配置'
  }

  const getModeTitle = () => {
    return mode === 'interview' ? 'AI 面试助手' : 'AI 会议助手'
  }

  // 将 AI 回复分为“思路”与“代码”两块，代码块使用 <pre> 单独样式显示
  const renderAiResponse = () => {
    if (!aiResponse) return null
    const marker = '代码 (java):'
    const [ideaPart, codePart] = aiResponse.split(marker)
    if (!codePart) {
      return (
        <div className="whitespace-pre-line">{aiResponse}</div>
      )
    }
    return (
      <>
        <div className="whitespace-pre-line mb-3 text-gray-800 text-sm leading-relaxed">{ideaPart.trim()}</div>
        <pre className="bg-gray-100 p-3 rounded text-xs overflow-x-auto"><code className="font-mono whitespace-pre">{codePart.trim()}</code></pre>
      </>
    )
  }

  if (isEmbedded) {
    return (
      <div className="h-full flex flex-col bg-white">

        {/* 嵌入模式的主内容 - 2:1比例 */}
        <div className="flex-1 overflow-hidden flex">
          {/* 左侧：AI回答区域 - 占2/3 */}
          <div className="w-2/3 flex flex-col border-r border-gray-100/50">
            <div className="px-6 py-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/50">
                  <Brain className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-base font-medium text-gray-700/80">{t.collaboration.aiSuggestions}</h3>
                  <p className="text-sm text-gray-600/80">
                    {mode === 'interview' ? t.collaboration.interviewSuggestionDesc : t.collaboration.meetingSuggestionDesc}
                  </p>
                </div>
              </div>
            </div>

            <div ref={aiResponseRef} className="flex-1 p-6 overflow-y-auto bg-white">
              {!aiResponse ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Brain className="w-8 h-8 text-blue-600" />
                    </div>
                    <p className="text-lg text-gray-600">{t.collaboration.assistantReady}</p>
                  </div>
                </div>
              ) : (
                <div className="h-full p-4">
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-200 shadow-sm h-full flex flex-col">
                    <div className="flex-1 p-4 overflow-y-auto">
                      {renderAiResponse()}
                    </div>
                    <div className="flex items-center justify-between p-3 border-t border-gray-200 bg-white/50 rounded-b-xl">
                      <div className="text-xs text-blue-600 font-medium">AI建议</div>
                      <button
                        onClick={() => navigator.clipboard.writeText(aiResponse)}
                        className="p-1 hover:bg-white rounded transition-colors"
                        title="复制"
                      >
                        <Copy className="w-3 h-3 text-gray-400" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：转录区域 - 占1/3 */}
          <div className="w-1/3 flex flex-col">
            <div className="px-4 py-3">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/50">
                  <Mic className={`w-4 h-4 ${isConnected ? 'text-green-600' : 'text-gray-400'}`} />
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-700/80">{t.collaboration.realTimeTranscription}</h3>
                  <p className="text-sm text-gray-600/80">
                    {mode === 'interview' ? t.collaboration.interviewTranscriptionDesc : t.collaboration.meetingTranscriptionDesc}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex-1 flex flex-col bg-white/50 backdrop-blur-sm">
              {/* 转录内容 - 统一显示 */}
              <div ref={transcriptionRef} className="flex-1 p-4 overflow-y-auto">
                {/* 只显示当前转录内容 */}
                {!transcription ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Mic className="w-8 h-8 text-green-600" />
                      </div>
                      <p className="text-lg text-gray-600">
                        {isConnected ? t.collaboration.listeningAudio : t.collaboration.waitingForAudio}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="h-full p-4">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm h-full flex flex-col">
                      <div className="flex-1 p-4 overflow-y-auto">
                        <div className="text-sm text-gray-800 leading-relaxed">
                          {transcription}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 border-t border-gray-200 bg-white/50 rounded-b-xl">
                        <div className="flex items-center">
                          <div className="text-xs text-blue-600 font-medium mr-2">正在转录...</div>
                          {/* 流式效果指示器 */}
                          <div className="flex space-x-1">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>


      </div>
    )
  }

  return (
    <div className="fixed inset-0 z-50 bg-white bg-opacity-90 flex items-center justify-center p-4">
      <div
        className="rounded-lg overflow-hidden shadow-xl border border-gray-200 flex flex-col bg-white"
        style={{
          height: '80vh',
          width: '90vw',
          maxWidth: '1200px'
        }}
      >
        {/* 顶部状态栏 */}
        <div className="bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-100 shadow-sm">
          <div className="px-6 py-3">
            <div className="flex items-center justify-between">
              {/* 左侧：状态信息 */}
              <div className="flex items-center space-x-6">
                {/* 状态指示器和标题 */}
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'} shadow-lg`} />
                    {isConnected && (
                      <div className="absolute inset-0 w-3 h-3 rounded-full bg-green-500 animate-ping opacity-75" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <h1 className="text-lg font-bold text-gray-900">{getModeTitle()}</h1>
                    <span className="text-gray-400">•</span>
                    <span className={`text-sm font-medium ${isConnected ? 'text-green-700' : 'text-red-600'}`}>
                      {isConnected ? '已连接' : '未连接'}
                    </span>
                    <span className="text-gray-400">•</span>
                    <span className="text-sm text-gray-600">{status}</span>
                  </div>
                </div>

                {/* 服务信息 */}
                <div className="bg-white rounded-lg px-3 py-2 shadow-sm border border-gray-100">
                  <div className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-blue-500" />
                    <span className="text-xs font-medium text-gray-700">服务:</span>
                    <span className="text-xs font-semibold text-blue-600">{getServiceDisplayName()}</span>
                  </div>
                </div>
              </div>

              {/* 右侧：操作按钮 */}
              <div className="flex items-center space-x-2">
                {/* 面试OCR功能按钮 */}
                {mode === 'interview' && (
                  <>
                    <button
                      onClick={async () => {
                        try {
                          console.log('🎯 Starting interview OCR fullscreen...');
                          await window.geekAssistant.interviewOCRFullscreen();
                        } catch (error) {
                          console.error('Failed to run interview OCR:', error);
                        }
                      }}
                      className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                      title="全屏截图面试分析 (Cmd+Shift+C)"
                    >
                      <div className="w-4 h-4 text-gray-500 group-hover:text-blue-600">📸</div>
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          console.log('🎯 Starting interview OCR area...');
                          await window.geekAssistant.interviewOCRArea();
                        } catch (error) {
                          console.error('Failed to run interview OCR area:', error);
                        }
                      }}
                      className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                      title="区域截图面试分析 (Cmd+Shift+A)"
                    >
                      <div className="w-4 h-4 text-gray-500 group-hover:text-green-600">🎯</div>
                    </button>
                  </>
                )}

                {!isGeminiConnected && (
                  <button
                    onClick={handleReconnect}
                    className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                    title="重新连接"
                  >
                    <RotateCcw className="w-4 h-4 text-gray-500 group-hover:text-blue-600" />
                  </button>
                )}
                <button
                  onClick={handleClearHistory}
                  className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                  title="清空历史"
                >
                  <Trash2 className="w-4 h-4 text-gray-500 group-hover:text-red-600" />
                </button>
                <button
                  onClick={onClose}
                  className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                  title="关闭"
                >
                  <X className="w-4 h-4 text-gray-500 group-hover:text-gray-700" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 主内容区域 - 2:1比例 */}
        <div className="flex-1 overflow-hidden flex">
          {/* 左侧：AI回答区域 - 占2/3 */}
          <div className="w-2/3 flex flex-col border-r border-gray-100/50">
            <div className="px-6 py-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/50">
                  <Brain className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-base font-medium text-gray-700/80">AI 智能建议</h3>
                  <p className="text-sm text-gray-600/80">
                    {mode === 'interview' ? '基于面试问题的回答建议' : '基于会议内容的回复建议'}
                  </p>
                </div>
              </div>
            </div>

            <div ref={aiResponseRef} className="flex-1 p-6 overflow-y-auto bg-white">
              {!aiResponse ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Brain className="w-8 h-8 text-blue-600" />
                    </div>
                    <h4 className="text-lg font-medium text-gray-900 mb-2">AI 助手准备就绪</h4>
                    <p className="text-gray-600 max-w-md">
                      {mode === 'interview'
                        ? '正在等待面试问题，我将为您提供专业的回答建议和关键要点'
                        : '正在等待会议内容，我将为您提供智能的回复建议和要点总结'}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-full p-4">
                  <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-200 shadow-sm h-full flex flex-col">
                    <div className="flex-1 p-4 overflow-y-auto">
                      {renderAiResponse()}
                    </div>
                    <div className="flex items-center justify-between p-3 border-t border-gray-200 bg-white/50 rounded-b-xl">
                      <div className="text-xs text-blue-600 font-medium">AI建议</div>
                      <button
                        onClick={() => navigator.clipboard.writeText(aiResponse)}
                        className="p-1 hover:bg-white rounded transition-colors"
                        title="复制"
                      >
                        <Copy className="w-3 h-3 text-gray-400" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：转录区域 - 占1/3 */}
          <div className="w-1/3 flex flex-col">
            <div className="px-4 py-3">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border border-white/50">
                  <Mic className={`w-4 h-4 ${isConnected ? 'text-green-600' : 'text-gray-400'}`} />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700/80">实时转录</h3>
                  <div className={`flex items-center space-x-1 text-xs ${isConnected ? 'text-green-600' : 'text-gray-500'}`}>
                    <div className={`w-1.5 h-1.5 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
                    <span>{isConnected ? '正在监听' : '未连接'}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex-1 flex flex-col bg-white/50 backdrop-blur-sm">
              {/* 转录内容 - 统一显示 */}
              <div ref={transcriptionRef} className="flex-1 p-4 overflow-y-auto">
                {/* 只显示当前转录内容 */}
                {!transcription ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Mic className="w-8 h-8 text-green-600" />
                      </div>
                      <p className="text-lg text-gray-600">
                        {isConnected ? t.collaboration.listeningAudio : t.collaboration.waitingForAudio}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="h-full p-4">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm h-full flex flex-col">
                      <div className="flex-1 p-4 overflow-y-auto">
                        <div className="text-sm text-gray-800 leading-relaxed">
                          {transcription}
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 border-t border-gray-200 bg-white/50 rounded-b-xl">
                        <div className="flex items-center">
                          <div className="text-xs text-blue-600 font-medium mr-2">正在转录...</div>
                          {/* 流式效果指示器 */}
                          <div className="flex space-x-1">
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                            <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  )
}

export default CollaborationMode
