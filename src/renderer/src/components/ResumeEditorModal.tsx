import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Save, User, Mail, Phone, MapPin, Calendar, Building, GraduationCap, Award, Plus, Trash2, Check, AlertCircle } from 'lucide-react'

interface Experience {
  id: string
  company: string
  position: string
  startDate: string
  endDate: string
  current: boolean
  description: string
  achievements: string[]
}

interface Education {
  id: string
  school: string
  degree: string
  major: string
  startDate: string
  endDate: string
  gpa?: string
}

interface Project {
  id: string
  name: string
  description: string
  technologies: string[]
  startDate: string
  endDate: string
  url?: string
}

interface ResumeData {
  id?: string
  updatedAt?: string
  personalInfo: {
    name: string
    email: string
    phone: string
    location: string
    summary: string
  }
  experiences: Experience[]
  education: Education[]
  projects: Project[]
  skills: string[]
  languages: string[]
  certifications: string[]
}

interface ResumeEditorModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (resume: ResumeData) => void
  initialData?: ResumeData | null
}

const ResumeEditorModal: React.FC<ResumeEditorModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData
}) => {
  const [activeTab, setActiveTab] = useState<'personal' | 'experience' | 'education' | 'skills'>('personal')
  const [resumeData, setResumeData] = useState<ResumeData>({
    personalInfo: {
      name: '',
      email: '',
      phone: '',
      location: '',
      summary: ''
    },
    experiences: [],
    education: [],
    projects: [],
    skills: [],
    languages: [],
    certifications: []
  })

  useEffect(() => {
    if (initialData) {
      setResumeData(initialData)
    } else {
      // 重置为空数据
      setResumeData({
        personalInfo: {
          name: '',
          email: '',
          phone: '',
          location: '',
          summary: ''
        },
        experiences: [],
        education: [],
        projects: [],
        skills: [],
        languages: [],
        certifications: []
      })
    }
  }, [initialData, isOpen])

  if (!isOpen) return null

  const handlePersonalInfoChange = (field: keyof ResumeData['personalInfo'], value: string) => {
    setResumeData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value
      }
    }))
  }

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      company: '',
      position: '',
      startDate: '',
      endDate: '',
      current: false,
      description: '',
      achievements: []
    }
    setResumeData(prev => ({
      ...prev,
      experiences: [...prev.experiences, newExperience]
    }))
  }

  const updateExperience = (id: string, field: keyof Experience, value: any) => {
    setResumeData(prev => ({
      ...prev,
      experiences: prev.experiences.map(exp =>
        exp.id === id ? { ...exp, [field]: value } : exp
      )
    }))
  }

  const removeExperience = (id: string) => {
    setResumeData(prev => ({
      ...prev,
      experiences: prev.experiences.filter(exp => exp.id !== id)
    }))
  }

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      school: '',
      degree: '',
      major: '',
      startDate: '',
      endDate: '',
      gpa: ''
    }
    setResumeData(prev => ({
      ...prev,
      education: [...prev.education, newEducation]
    }))
  }

  const updateEducation = (id: string, field: keyof Education, value: string) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.map(edu =>
        edu.id === id ? { ...edu, [field]: value } : edu
      )
    }))
  }

  const removeEducation = (id: string) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.filter(edu => edu.id !== id)
    }))
  }

  type SkillCategory = 'skills' | 'languages' | 'certifications'
  
  const addSkill = (category: SkillCategory) => {
    const skill = prompt(`添加${category === 'skills' ? '技能' : category === 'languages' ? '语言' : '证书'}:`)
    if (skill && skill.trim()) {
      setResumeData(prev => ({
        ...prev,
        [category]: [...prev[category], skill.trim()]
      }))
    }
  }

  const removeSkill = (category: SkillCategory, index: number) => {
    setResumeData(prev => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index)
    }))
  }

  const handleSave = async () => {
    if (!validateForm()) {
      // 滚动到第一个错误处
      const firstError = Object.keys(formErrors)[0]
      if (firstError) {
        document.getElementById(firstError)?.scrollIntoView({ 
          behavior: 'smooth',
          block: 'center'
        })
      }
      return
    }
    
    await handleSubmit()
  }



  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const modalRef = useRef<HTMLDivElement>(null)
  
  // Define tab type and tabs data
  type TabType = 'personal' | 'experience' | 'education' | 'skills'
  
  const tabs = [
    { id: 'personal' as const, label: '个人信息', icon: User },
    { id: 'experience' as const, label: '工作经历', icon: Building },
    { id: 'education' as const, label: '教育背景', icon: GraduationCap },
    { id: 'skills' as const, label: '技能证书', icon: Award }
  ]

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose()
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  const validateForm = () => {
    const errors: Record<string, string> = {}
    if (!resumeData.personalInfo.name.trim()) errors.name = '请输入姓名'
    if (!resumeData.personalInfo.email.trim()) {
      errors.email = '请输入邮箱'
    } else if (!/\S+@\S+\.\S+/.test(resumeData.personalInfo.email)) {
      errors.email = '邮箱格式不正确'
    }
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (): Promise<void> => {
    if (!validateForm()) return
    
    setIsSubmitting(true)
    try {
      const finalData: ResumeData = {
        ...resumeData,
        id: initialData?.id || Date.now().toString(),
        updatedAt: new Date().toISOString()
      }
      await onSave(finalData)
      onClose()
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 overflow-y-auto">
      <motion.div 
        ref={modalRef}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden border border-gray-100 flex flex-col"
      >
        {/* 头部 */}
        <div className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white p-6 relative">
          <div className="flex items-start justify-between">
            <div>
              <h2 className="text-2xl font-bold">
                {initialData ? '编辑个人简历' : '创建专业简历'}
              </h2>
              <p className="text-blue-100 mt-1">完善您的个人信息，为面试做好准备</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-full transition-all"
              aria-label="关闭"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
          {/* 左侧标签页 - 移动端优化 */}
          <div className="w-full md:w-64 bg-gray-50 border-b md:border-r border-gray-200 flex-shrink-0 overflow-x-auto">
            <div className="p-2 md:p-4">
              <div className="flex md:block space-x-2 md:space-x-0 md:space-y-2 overflow-x-auto">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  const isActive = activeTab === tab.id
                  return (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-sm border border-blue-100'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                      }`}
                    >
                      <div className={`p-1.5 rounded-lg ${
                        isActive 
                          ? 'bg-blue-100 text-blue-600' 
                          : 'bg-gray-100 text-gray-500'
                      }`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <span className="font-medium text-sm whitespace-nowrap">{tab.label}</span>
                      {isActive && (
                        <motion.span 
                          className="ml-auto w-1.5 h-1.5 bg-blue-500 rounded-full"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                        />
                      )}
                    </motion.button>
                  )
                })}
              </div>
            </div>
          </div>

          {/* 右侧内容 */}
          <div className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-white to-gray-50">
            <div className="p-6">
              {/* 标签页内容 */}
              <AnimatePresence mode="wait">
                {activeTab === 'personal' && (
                  <motion.div
                    key="personal-tab"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    className="space-y-6"
                  >
                    {/* 姓名 */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 flex items-center">
                        <User className="w-4 h-4 mr-1.5 text-blue-600" />
                        姓名 <span className="text-red-500 ml-1">*</span>
                      </label>
                      {formErrors.name && (
                        <span className="text-xs text-red-500 flex items-center">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          {formErrors.name}
                        </span>
                      )}
                    </div>
                    <div className="relative">
                      <input
                        id="name"
                        type="text"
                        value={resumeData.personalInfo.name}
                        onChange={(e) => {
                          handlePersonalInfoChange('name', e.target.value)
                          if (formErrors.name) {
                            setFormErrors(prev => ({
                              ...prev,
                              name: ''
                            }))
                          }
                        }}
                        className={`w-full px-4 py-2.5 text-sm border ${
                          formErrors.name 
                            ? 'border-red-300 focus:ring-red-500' 
                            : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                        } rounded-xl focus:ring-2 focus:outline-none transition-all duration-200`}
                        placeholder="例如：张三"
                      />
                      {resumeData.personalInfo.name && !formErrors.name && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                          <Check className="h-4 w-4 text-green-500" />
                        </div>
                      )}
                    </div>

                    {/* 邮箱 */}
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <label className="block text-sm font-medium text-gray-700 flex items-center">
                          <Mail className="w-4 h-4 mr-1.5 text-blue-600" />
                          邮箱 <span className="text-red-500 ml-1">*</span>
                        </label>
                        {formErrors.email && (
                          <span className="text-xs text-red-500 flex items-center">
                            <AlertCircle className="w-3 h-3 mr-1" />
                            {formErrors.email}
                          </span>
                        )}
                      </div>
                      <div className="relative">
                        <input
                          id="email"
                          type="email"
                          value={resumeData.personalInfo.email}
                          onChange={(e) => {
                            handlePersonalInfoChange('email', e.target.value)
                            if (formErrors.email) {
                              setFormErrors(prev => ({
                                ...prev,
                                email: ''
                              }))
                            }
                          }}
                          className={`w-full px-4 py-2.5 text-sm border ${
                            formErrors.email 
                              ? 'border-red-300 focus:ring-red-500' 
                              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                          } rounded-xl focus:ring-2 focus:outline-none transition-all duration-200`}
                          placeholder="<EMAIL>"
                        />
                        {resumeData.personalInfo.email && !formErrors.email && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <Check className="h-4 w-4 text-green-500" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 电话 */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 flex items-center">
                        <Phone className="w-4 h-4 mr-1.5 text-blue-600" />
                        电话
                      </label>
                      <div className="relative">
                        <input
                          type="tel"
                          value={resumeData.personalInfo.phone}
                          onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
                          className="w-full px-4 py-2.5 text-sm border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200"
                          placeholder="13800138000"
                        />
                        {resumeData.personalInfo.phone && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <Check className="h-4 w-4 text-green-500" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 所在地 */}
                    <div className="space-y-1">
                      <label className="block text-sm font-medium text-gray-700 flex items-center">
                        <MapPin className="w-4 h-4 mr-1.5 text-blue-600" />
                        所在地
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={resumeData.personalInfo.location}
                          onChange={(e) => handlePersonalInfoChange('location', e.target.value)}
                          className="w-full px-4 py-2.5 text-sm border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200"
                          placeholder="例如：北京 朝阳区"
                        />
                        {resumeData.personalInfo.location && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <Check className="h-4 w-4 text-green-500" />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 个人简介 */}
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      个人简介
                    </label>
                    <div className="relative">
                      <textarea
                        value={resumeData.personalInfo.summary}
                        onChange={(e) => handlePersonalInfoChange('summary', e.target.value)}
                        rows={4}
                        className="w-full px-4 py-3 text-sm border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none transition-all duration-200 resize-none"
                        placeholder="简要介绍您的专业背景、技能特长和职业目标..."
                      />
                      {resumeData.personalInfo.summary && (
                        <div className="absolute bottom-3 right-3 flex items-center">
                          <span className="text-xs text-gray-400">
                            {resumeData.personalInfo.summary.length}/500
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  </motion.div>
                )}

                {activeTab === 'experience' && (
                <motion.div
                  key="experience-tab"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">工作经历</h3>
                    <button
                      onClick={addExperience}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>添加经历</span>
                    </button>
                  </div>

                  {resumeData.experiences.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Building className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <p>还没有添加工作经历</p>
                      <p className="text-sm">点击上方按钮添加您的工作经历</p>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {resumeData.experiences.map((exp, index) => (
                        <div key={exp.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900">工作经历 {index + 1}</h4>
                            <button
                              type="button"
                              onClick={() => removeExperience(exp.id)}
                              className="text-red-600 hover:text-red-800 transition-colors"
                              aria-label="删除工作经历"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">公司名称</label>
                              <input
                                type="text"
                                value={exp.company}
                                onChange={(e) => updateExperience(exp.id, 'company', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="公司名称"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">职位</label>
                              <input
                                type="text"
                                value={exp.position}
                                onChange={(e) => updateExperience(exp.id, 'position', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="职位名称"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                              <input
                                type="month"
                                value={exp.startDate}
                                onChange={(e) => updateExperience(exp.id, 'startDate', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                              <div className="space-y-2">
                                <input
                                  type="month"
                                  value={exp.endDate}
                                  onChange={(e) => updateExperience(exp.id, 'endDate', e.target.value)}
                                  disabled={exp.current}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
                                />
                                <label className="flex items-center space-x-2">
                                  <input
                                    type="checkbox"
                                    checked={exp.current}
                                    onChange={(e) => updateExperience(exp.id, 'current', e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                  <span className="text-sm text-gray-700">目前在职</span>
                                </label>
                              </div>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">工作描述</label>
                            <textarea
                              value={exp.description}
                              onChange={(e) => updateExperience(exp.id, 'description', e.target.value)}
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="描述您的工作职责、主要成就等..."
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </motion.div>
                )}

                {activeTab === 'education' && (
                <motion.div
                  key="education-tab"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">教育背景</h3>
                    <button
                      onClick={addEducation}
                      className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>添加教育经历</span>
                    </button>
                  </div>

                  {resumeData.education.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <GraduationCap className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <p>还没有添加教育背景</p>
                      <p className="text-sm">点击上方按钮添加您的教育经历</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {resumeData.education.map((edu, index) => (
                        <div key={edu.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="font-medium text-gray-900">教育经历 {index + 1}</h4>
                            <button
                              type="button"
                              onClick={() => removeEducation(edu.id)}
                              className="text-red-600 hover:text-red-800 transition-colors"
                              aria-label="删除教育经历"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">学校名称</label>
                              <input
                                type="text"
                                value={edu.school}
                                onChange={(e) => updateEducation(edu.id, 'school', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="学校名称"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">学位</label>
                              <input
                                type="text"
                                value={edu.degree}
                                onChange={(e) => updateEducation(edu.id, 'degree', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="学士/硕士/博士"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">专业</label>
                              <input
                                type="text"
                                value={edu.major}
                                onChange={(e) => updateEducation(edu.id, 'major', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="专业名称"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">GPA（可选）</label>
                              <input
                                type="text"
                                value={edu.gpa || ''}
                                onChange={(e) => updateEducation(edu.id, 'gpa', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="3.8/4.0"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                              <input
                                type="month"
                                value={edu.startDate}
                                onChange={(e) => updateEducation(edu.id, 'startDate', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                              <input
                                type="month"
                                value={edu.endDate}
                                onChange={(e) => updateEducation(edu.id, 'endDate', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </motion.div>
                )}

                {activeTab === 'skills' && (
                <motion.div
                  key="skills-tab"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <h3 className="text-lg font-semibold text-gray-900">技能与证书</h3>

                  {/* 技能 */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">专业技能</h4>
                      <button
                        onClick={() => addSkill('skills')}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        + 添加技能
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {resumeData.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                        >
                          {skill}
                          <button
                            type="button"
                            onClick={() => removeSkill('skills', index)}
                            className="ml-2 text-blue-600 hover:text-red-600"
                            aria-label="删除技能"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                      {resumeData.skills.length === 0 && (
                        <p className="text-gray-500 text-sm">还没有添加技能</p>
                      )}
                    </div>
                  </div>

                  {/* 语言 */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">语言能力</h4>
                      <button
                        onClick={() => addSkill('languages')}
                        className="text-green-600 hover:text-green-800 text-sm"
                      >
                        + 添加语言
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {resumeData.languages.map((language, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full"
                        >
                          {language}
                          <button
                            type="button"
                            onClick={() => removeSkill('languages', index)}
                            className="ml-2 text-green-600 hover:text-red-600"
                            aria-label="删除语言"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                      {resumeData.languages.length === 0 && (
                        <p className="text-gray-500 text-sm">还没有添加语言</p>
                      )}
                    </div>
                  </div>

                  {/* 证书 */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">证书资质</h4>
                      <button
                        onClick={() => addSkill('certifications')}
                        className="text-purple-600 hover:text-purple-800 text-sm"
                      >
                        + 添加证书
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {resumeData.certifications.map((cert, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full"
                        >
                          {cert}
                          <button
                            type="button"
                            onClick={() => removeSkill('certifications', index)}
                            className="ml-2 text-purple-600 hover:text-red-600"
                            aria-label="删除证书"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                      {resumeData.certifications.length === 0 && (
                        <p className="text-gray-500 text-sm">还没有添加证书</p>
                      )}
                    </div>
                  </div>
                </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {resumeData.personalInfo.name && (
                <span className="flex items-center">
                  <span className="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  自动保存已启用
                </span>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                取消
              </button>
              <button
                type="button"
                onClick={handleSave}
                disabled={Object.keys(formErrors).length > 0}
                className={`px-6 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${
                  Object.keys(formErrors).length > 0
                    ? 'bg-blue-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    保存中...
                  </span>
                ) : '保存简历'}
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default ResumeEditorModal
