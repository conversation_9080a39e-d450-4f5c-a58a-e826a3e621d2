import React, { useState } from 'react'
import { X, Save, User, Briefcase, GraduationCap, FileText } from 'lucide-react'

interface ResumeInputModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (resumeData: any) => void
  initialData?: any
  isEmbedded?: boolean
  currentLanguage?: string
  onLanguageChange?: (lang: string) => void
}

const ResumeInputModal: React.FC<ResumeInputModalProps> = ({ isOpen, onClose, onSave, initialData, isEmbedded = false, currentLanguage = 'zh', onLanguageChange }) => {
  const [activeTab, setActiveTab] = useState(0)
  const [formData, setFormData] = useState({
    name: initialData?.personalInfo?.name || initialData?.name || '',
    email: initialData?.personalInfo?.email || initialData?.email || '',
    phone: initialData?.personalInfo?.phone || initialData?.phone || '',
    location: initialData?.personalInfo?.location || initialData?.location || '',
    summary: initialData?.personalInfo?.summary || initialData?.summary || '',
    experience: initialData?.experience || '',
    education: initialData?.education || '',
    skills: initialData?.skills || '',
    projects: initialData?.projects || ''
  })

  const tabs = [
    { id: 0, name: '基本信息', icon: User },
    { id: 1, name: '工作经验', icon: Briefcase },
    { id: 2, name: '教育背景', icon: GraduationCap },
    { id: 3, name: '技能特长', icon: '⚡' },
    { id: 4, name: '项目经验', icon: '🚀' }
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = () => {
    const resumeData = {
      id: initialData?.id || Date.now().toString(),
      personalInfo: {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        location: formData.location,
        summary: formData.summary
      },
      experiences: initialData?.experiences || [],
      education: initialData?.education || [],
      projects: initialData?.projects || [],
      skills: formData.skills ? formData.skills.split(',').map(s => s.trim()) : [],
      languages: initialData?.languages || [],
      certifications: initialData?.certifications || [],
      updatedAt: new Date().toISOString()
    }
    onSave(resumeData)
    onClose()
  }

  if (!isOpen) return null

  if (isEmbedded) {
    return (
      <div className="h-full overflow-hidden p-4">
        <div className="max-w-7xl mx-auto h-full flex flex-col">
          {/* 顶部导航栏 */}
          <div className="flex items-center justify-between mb-2">
            <button
              onClick={onClose}
              className="flex items-center space-x-2 text-gray-600 hover:text-black transition-colors"
            >
              <span>←</span>
              <span className="font-medium">返回</span>
            </button>

            <button
              onClick={() => onLanguageChange && onLanguageChange(currentLanguage === 'zh' ? 'en' : 'zh')}
              className="px-3 py-1 text-sm text-gray-600 hover:text-black border border-gray-300 rounded-md transition-colors"
            >
              {currentLanguage === 'zh' ? 'EN' : '中文'}
            </button>
          </div>

          {/* 页面标题 */}
          <div className="text-center mb-6">
            <h1 className="text-xl font-bold text-black">个人简历编辑</h1>
          </div>

          {/* 左右布局主体 - 融入背景 */}
          <div className="flex-1 flex gap-6 overflow-hidden">
            {/* 左侧Tab导航 - 卡片式设计 */}
            <div className="w-80 space-y-3">
              <h3 className="text-lg font-bold text-black mb-4">编辑内容</h3>
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                    activeTab === tab.id
                      ? 'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200'
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      activeTab === tab.id
                        ? 'bg-blue-100'
                        : 'bg-gray-100'
                    }`}>
                      {typeof tab.icon === 'string' ? (
                        <span className="text-lg">{tab.icon}</span>
                      ) : (
                        <tab.icon className={`w-5 h-5 ${activeTab === tab.id ? 'text-blue-600' : 'text-gray-600'}`} />
                      )}
                    </div>
                    <div>
                      <h4 className={`font-semibold ${activeTab === tab.id ? 'text-blue-900' : 'text-gray-900'}`}>
                        {tab.name}
                      </h4>
                      <p className="text-xs text-gray-600">
                        {tab.id === 0 && '基本信息和个人简介'}
                        {tab.id === 1 && '工作经历和成就'}
                        {tab.id === 2 && '教育背景和学历'}
                        {tab.id === 3 && '专业技能和特长'}
                        {tab.id === 4 && '项目经验和成果'}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 右侧内容区域 - 融入背景 */}
            <div className="flex-1 overflow-y-auto">
              {/* Tab 0: 基本信息 */}
              {activeTab === 0 && (
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <User className="w-5 h-5 text-blue-600" />
                      </div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">基本信息</h3>
                    </div>
                    <p className="text-sm text-gray-600 ml-13">填写您的个人基本信息和简介</p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">姓名 *</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md"
                        placeholder="请输入您的姓名"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">邮箱 *</label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md"
                        placeholder="请输入您的邮箱"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">电话</label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md"
                        placeholder="请输入您的电话号码"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">地址</label>
                      <input
                        type="text"
                        value={formData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md"
                        placeholder="请输入您的地址"
                      />
                    </div>
                  </div>

                  {/* 个人简介 */}
                  <div className="mt-6 space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">个人简介</label>
                    <textarea
                      value={formData.summary}
                      onChange={(e) => handleInputChange('summary', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md resize-none"
                      placeholder="简要介绍您的专业背景、技能特长和职业目标..."
                    />
                  </div>
                </div>
              )}

              {/* Tab 1: 工作经验 */}
              {activeTab === 1 && (
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <span className="text-lg">💼</span>
                      </div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">工作经验</h3>
                    </div>
                    <p className="text-sm text-gray-600 ml-13">详细描述您的工作经历和成就</p>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">工作经历</label>
                    <textarea
                      value={formData.experience}
                      onChange={(e) => handleInputChange('experience', e.target.value)}
                      rows={10}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md resize-none"
                      placeholder="请详细描述您的工作经历，包括：&#10;• 公司名称、职位、工作时间&#10;• 主要职责和工作内容&#10;• 重要成就和项目经验&#10;• 技能提升和学习收获"
                    />
                  </div>
                </div>
              )}

              {/* Tab 2: 教育背景 */}
              {activeTab === 2 && (
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <span className="text-lg">🎓</span>
                      </div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">教育背景</h3>
                    </div>
                    <p className="text-sm text-gray-600 ml-13">填写您的教育经历和学术成就</p>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">教育经历</label>
                    <textarea
                      value={formData.education}
                      onChange={(e) => handleInputChange('education', e.target.value)}
                      rows={8}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md resize-none"
                      placeholder="请描述您的教育背景，包括：&#10;• 学校名称和专业&#10;• 学历层次（本科/硕士/博士）&#10;• 就读时间和毕业时间&#10;• 主要课程、GPA或学术成就"
                    />
                  </div>
                </div>
              )}

              {/* Tab 3: 技能特长 */}
              {activeTab === 3 && (
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <span className="text-lg">⚡</span>
                      </div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">技能特长</h3>
                    </div>
                    <p className="text-sm text-gray-600 ml-13">列出您的专业技能和特长</p>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">专业技能</label>
                    <textarea
                      value={formData.skills}
                      onChange={(e) => handleInputChange('skills', e.target.value)}
                      rows={8}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md resize-none"
                      placeholder="请列出您的技能，例如：&#10;• 编程语言：Java, Python, JavaScript&#10;• 框架技术：Spring Boot, React, Vue.js&#10;• 数据库：MySQL, MongoDB, Redis&#10;• 工具软件：Git, Docker, Jenkins"
                    />
                  </div>
                </div>
              )}

              {/* Tab 4: 项目经验 */}
              {activeTab === 4 && (
                <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <span className="text-lg">🚀</span>
                      </div>
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">项目经验</h3>
                    </div>
                    <p className="text-sm text-gray-600 ml-13">描述您的重要项目和成果</p>
                  </div>
                  <div className="space-y-2">
                    <label className="block text-sm font-semibold text-gray-700">项目经历</label>
                    <textarea
                      value={formData.projects}
                      onChange={(e) => handleInputChange('projects', e.target.value)}
                      rows={10}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-400 shadow-sm hover:shadow-md resize-none"
                      placeholder="请描述您的项目经验，包括：&#10;• 项目名称和描述&#10;• 项目时间和规模&#10;• 您的角色和职责&#10;• 使用的技术栈&#10;• 项目成果和收获"
                    />
                  </div>
                </div>
              )}
          </div>
        </div>

          {/* 融入背景的底部操作栏 */}
          <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-6">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span className="text-lg">💡</span>
              <span>完善的简历信息有助于获得更精准的面试建议</span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-6 py-2 text-gray-600 hover:text-black hover:bg-gray-100 rounded-lg transition-all duration-200 font-medium"
              >
                取消
              </button>

              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                <Save className="w-4 h-4" />
                <span>保存简历</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 z-50 bg-white bg-opacity-90 flex items-center justify-center p-8">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-xl">
        {/* 头部 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                <User className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-black">个人简历信息</h1>
                <p className="text-sm text-gray-600">完善您的个人信息，获得更精准的面试建议</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
            >
              <X className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Tab导航 */}
        <div className="bg-white border-b border-gray-200 px-6">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-3 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-black text-black'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {typeof tab.icon === 'string' ? (
                  <span className="text-sm">{tab.icon}</span>
                ) : (
                  <tab.icon className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="h-[calc(90vh-180px)] overflow-y-auto p-6 bg-gray-50">
          <div className="max-w-2xl mx-auto">
            {/* Tab 0: 基本信息 */}
            {activeTab === 0 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">基本信息</h3>
                  <p className="text-sm text-gray-600">填写您的个人基本信息</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的姓名"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的邮箱"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">电话</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的电话"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">地址</label>
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="请输入您的地址"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Tab 1: 个人简介 */}
            {activeTab === 1 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">个人简介</h3>
                  <p className="text-sm text-gray-600">简要介绍您的专业背景和优势</p>
                </div>
                <textarea
                  value={formData.summary}
                  onChange={(e) => handleInputChange('summary', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请简要介绍您的专业背景、工作经验、技能特长和职业目标..."
                />
              </div>
            )}

            {/* Tab 2: 工作经验 */}
            {activeTab === 2 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">工作经验</h3>
                  <p className="text-sm text-gray-600">详细描述您的工作经历</p>
                </div>
                <textarea
                  value={formData.experience}
                  onChange={(e) => handleInputChange('experience', e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的工作经验，包括：&#10;• 公司名称和职位&#10;• 工作时间（如：2020年1月 - 2023年12月）&#10;• 主要职责和成就&#10;• 使用的技术和工具"
                />
              </div>
            )}

            {/* Tab 3: 教育背景 */}
            {activeTab === 3 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">教育背景</h3>
                  <p className="text-sm text-gray-600">填写您的教育经历</p>
                </div>
                <textarea
                  value={formData.education}
                  onChange={(e) => handleInputChange('education', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的教育背景，包括：&#10;• 学校名称&#10;• 专业和学历&#10;• 就读时间&#10;• 主要课程或成就"
                />
              </div>
            )}

            {/* Tab 4: 技能特长 */}
            {activeTab === 4 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">技能特长</h3>
                  <p className="text-sm text-gray-600">列出您的专业技能</p>
                </div>
                <textarea
                  value={formData.skills}
                  onChange={(e) => handleInputChange('skills', e.target.value)}
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请列出您的技能，例如：&#10;• 编程语言：Java, Python, JavaScript&#10;• 框架技术：Spring Boot, React, Vue.js&#10;• 数据库：MySQL, MongoDB, Redis&#10;• 工具软件：Git, Docker, Jenkins"
                />
              </div>
            )}

            {/* Tab 5: 项目经验 */}
            {activeTab === 5 && (
              <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-black mb-2">项目经验</h3>
                  <p className="text-sm text-gray-600">描述您参与的重要项目</p>
                </div>
                <textarea
                  value={formData.projects}
                  onChange={(e) => handleInputChange('projects', e.target.value)}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                  placeholder="请描述您的项目经验，包括：&#10;• 项目名称和描述&#10;• 项目时间和规模&#10;• 您的角色和职责&#10;• 使用的技术栈&#10;• 项目成果和收获"
                />
              </div>
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 px-6 py-4 bg-white">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              <span>💡 提示：完善的简历信息有助于获得更精准的面试建议</span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-black transition-colors"
              >
                取消
              </button>

              <button
                onClick={handleSave}
                className="btn-hover flex items-center space-x-2 px-4 py-2 bg-black text-white font-medium rounded-lg"
              >
                <Save className="w-4 h-4" />
                <span>保存简历</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResumeInputModal
