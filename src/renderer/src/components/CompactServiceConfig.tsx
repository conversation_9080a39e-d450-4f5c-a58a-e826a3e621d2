import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, EyeOff, <PERSON>ap, Mi<PERSON>, <PERSON>, Plus, Star } from 'lucide-react'
import OCRConfigPanel from './OCRConfigPanel'

interface ServiceConfig {
  mode: 'gemini-live' | 'separated'
  transcription?: string
  ai?: string
}

interface CompactServiceConfigProps {
  onClose?: () => void
}

const CompactServiceConfig: React.FC<CompactServiceConfigProps> = ({ onClose }) => {
  const [mode, setMode] = useState<'gemini-live' | 'separated'>('separated')
  const [transcriptionService, setTranscriptionService] = useState('deepgram')
  const [aiService, setAiService] = useState('groq')
  const [customAPIs, setCustomAPIs] = useState<any[]>([])
  const [showCustomAPIManager, setShowCustomAPIManager] = useState(false)
  const [showOCRConfig, setShowOCRConfig] = useState(false)

  useEffect(() => {
    loadCurrentConfig()
    loadCustomAPIs()
  }, [])

  const loadCurrentConfig = async () => {
    try {
      console.log('🔧 CompactServiceConfig: Loading current config...')
      if (window.geekAssistant?.getCurrentServiceConfig) {
        const config = await window.geekAssistant.getCurrentServiceConfig()
        if (config) {
          console.log('🔧 CompactServiceConfig: Loaded config:', config)
          if (config.mode === 'separated' && config.separated) {
            setMode('separated')
            setTranscriptionService(config.separated.transcription.provider)
            setAiService(config.separated.ai.provider)
          } else if (config.mode === 'gemini-live') {
            setMode('gemini-live')
          }
        } else {
          console.warn('🔧 CompactServiceConfig: No config found, reloading...')
          // 尝试重新加载配置
          const reloadedConfig = await window.geekAssistant.reloadServiceConfig()
          if (reloadedConfig) {
            console.log('🔧 CompactServiceConfig: Reloaded config:', reloadedConfig)
            if (reloadedConfig.mode === 'separated' && reloadedConfig.separated) {
              setMode('separated')
              setTranscriptionService(reloadedConfig.separated.transcription.provider)
              setAiService(reloadedConfig.separated.ai.provider)
            } else if (reloadedConfig.mode === 'gemini-live') {
              setMode('gemini-live')
            }
          }
        }
      }
    } catch (error) {
      console.error('🔧❌ CompactServiceConfig: Failed to load config:', error)
    }
  }

  const loadCustomAPIs = async () => {
    try {
      const apis = await window.geekAssistant?.customAPI?.getAll()
      if (apis) {
        setCustomAPIs(apis.filter(api => api.isActive))
      }
    } catch (error) {
      console.error('Failed to load custom APIs:', error)
    }
  }

  const updateServiceConfig = async (config: ServiceConfig) => {
    try {
      console.log('🔧 CompactServiceConfig: Updating service configuration:', config)
      
      let fullConfig: any
      
      if (config.mode === 'separated') {
        fullConfig = {
          mode: 'separated',
          separated: {
            transcription: {
              provider: config.transcription,
              config: {
                apiKey: getDefaultAPIKey(config.transcription),
                language: 'zh-CN',
                realtime: true
              }
            },
            ai: {
              provider: config.ai?.startsWith('custom-') ? 'custom' : config.ai,
              config: {
                apiKey: config.ai?.startsWith('custom-') ? '' : '********************************************************',
                model: config.ai === 'groq' ? 'llama-3.1-8b-instant' : 'gpt-3.5-turbo',
                temperature: 0.7,
                maxTokens: 500,
                customId: config.ai?.startsWith('custom-') ? config.ai : undefined
              }
            }
          }
        }
      } else {
        fullConfig = {
          mode: 'gemini-live',
          geminiLive: {
            apiKey: 'AIzaSyDxcxP-FViBZOUw6s2Obsji5lllDS1QOiw',
            model: 'gemini-live-2.5-flash-preview',
            language: 'cmn-CN',
            customPrompt: '',
            profile: 'interview'
          }
        }
      }

      console.log('🔧 CompactServiceConfig: Saving config:', fullConfig)
      
      if (window.geekAssistant?.updateServiceConfig) {
        const success = await window.geekAssistant.updateServiceConfig(fullConfig)
        if (success) {
          console.log('🔧 CompactServiceConfig: Configuration saved successfully')
          // 更新本地状态
          if (fullConfig.mode === 'separated' && fullConfig.separated) {
            setMode('separated')
            setTranscriptionService(fullConfig.separated.transcription.provider)
            setAiService(fullConfig.separated.ai.provider)
          } else if (fullConfig.mode === 'gemini-live') {
            setMode('gemini-live')
          }
        }
      }
    } catch (error) {
      console.error('🔧 CompactServiceConfig: Failed to update config:', error)
    }
  }

  const getDefaultAPIKey = (provider: string) => {
    const keys: Record<string, string> = {
      'azure': '', // Azure使用账号池管理，不需要API密钥
      'speechmatics': 'hEMM081yWFGCKLSvSWs1Tox6FGk4PLn1',
      'gladia': 'da495f4d-893b-4aac-beb2-c3c313c143fe',
      'deepgram': '****************************************',
      'assemblyai': '********************************'
    }
    return keys[provider] || ''
  }

  // 临时切换到可用的服务
  const switchToWorkingService = async () => {
    console.log('🔧 Switching to Speechmatics (working service)...')
    await updateServiceConfig({
      mode: 'separated',
      transcription: 'speechmatics',
      ai: 'groq'
    })
  }

  const handleModeChange = (newMode: 'gemini-live' | 'separated') => {
    setMode(newMode)
    updateServiceConfig(
      newMode === 'gemini-live' 
        ? { mode: 'gemini-live' }
        : { mode: 'separated', transcription: transcriptionService, ai: aiService }
    )
  }

  const handleServiceChange = (type: 'transcription' | 'ai', serviceId: string) => {
    console.log('🔧 CompactServiceConfig: Service change triggered:', { type, serviceId })
    
    if (type === 'transcription') {
      setTranscriptionService(serviceId)
    } else {
      setAiService(serviceId)
    }

    const config = {
      mode: 'separated' as const,
      transcription: type === 'transcription' ? serviceId : transcriptionService,
      ai: type === 'ai' ? serviceId : aiService
    }
    
    console.log('🔧 CompactServiceConfig: Calling updateServiceConfig with:', config)
    updateServiceConfig(config)
  }

  const transcriptionServices = [
    {
      id: 'azure',
      name: 'Azure Speech',
      icon: '🔷',
      quota: '6.5小时免费',
      latency: '200ms',
      accuracy: '97%',
      recommended: true,
      description: '13个账号池'
    },
    {
      id: 'speechmatics',
      name: 'Speechmatics',
      icon: '🎙️',
      quota: '8小时/月',
      latency: '250ms',
      accuracy: '96%',
      recommended: true
    },
    {
      id: 'gladia',
      name: 'Gladia',
      icon: '🎵',
      quota: '10小时/月',
      latency: '300ms',
      accuracy: '94%',
      recommended: true
    },
    {
      id: 'deepgram',
      name: 'Deepgram',
      icon: '🌊',
      quota: '$200免费',
      latency: '300ms',
      accuracy: '95%',
      recommended: true
    },
    {
      id: 'assemblyai',
      name: 'AssemblyAI',
      icon: '🎯',
      quota: '5小时/月',
      latency: '500ms',
      accuracy: '92%'
    }
  ]

  const aiServices = [
    {
      id: 'groq',
      name: 'Groq',
      icon: '⚡',
      quota: '14.4K请求/天',
      latency: '100ms',
      quality: '90%',
      recommended: true
    },
    {
      id: 'together',
      name: 'Together AI',
      icon: '🤝',
      quota: '$25免费',
      latency: '800ms',
      quality: '92%'
    },
    {
      id: 'openai',
      name: 'OpenAI GPT',
      icon: '🧠',
      quota: '需付费',
      latency: '1000ms',
      quality: '95%'
    }
  ]

  return (
    <div className="fixed inset-0 z-50 bg-white">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Settings className="w-4 h-4 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">AI服务配置</h1>
            <p className="text-xs text-gray-600">选择最适合的服务方案</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowOCRConfig(true)}
            className="flex items-center space-x-1 px-3 py-1.5 rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-700 transition-colors text-sm"
            title="OCR识别配置"
          >
            <Eye className="w-4 h-4" />
            <span>OCR</span>
          </button>
          <button
            onClick={onClose}
            className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
          >
            <X className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="h-[calc(100vh-64px)] overflow-y-auto">
        <div className="p-4 space-y-6">
          {/* 服务模式选择 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* Gemini Live */}
            <div
              className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-200 p-4 ${
                mode === 'gemini-live'
                  ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg'
                  : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
              }`}
              onClick={() => handleModeChange('gemini-live')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Zap className="w-5 h-5" />
                  <div>
                    <h3 className="font-semibold text-sm">Google Gemini Live</h3>
                    <p className={`text-xs ${mode === 'gemini-live' ? 'text-white/80' : 'text-gray-600'}`}>
                      一体化实时语音AI
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {mode === 'gemini-live' && <Check className="w-4 h-4 text-green-300" />}
                  <div className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${
                    mode === 'gemini-live' ? 'bg-yellow-400 text-yellow-900' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    <Star className="w-3 h-3" />
                    <span>推荐</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div>
                  <span className={mode === 'gemini-live' ? 'text-white/70' : 'text-gray-500'}>延迟</span>
                  <div className="font-medium text-green-400">{'< 500ms'}</div>
                </div>
                <div>
                  <span className={mode === 'gemini-live' ? 'text-white/70' : 'text-gray-500'}>准确率</span>
                  <div className="font-medium text-blue-400">95%+</div>
                </div>
              </div>
            </div>

            {/* 分离式服务 */}
            <div
              className={`relative overflow-hidden rounded-xl cursor-pointer transition-all duration-200 p-4 ${
                mode === 'separated'
                  ? 'bg-gradient-to-br from-green-500 to-teal-600 text-white shadow-lg'
                  : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
              }`}
              onClick={() => handleModeChange('separated')}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Settings className="w-5 h-5" />
                  <div>
                    <h3 className="font-semibold text-sm">分离式服务</h3>
                    <p className={`text-xs ${mode === 'separated' ? 'text-white/80' : 'text-gray-600'}`}>
                      自由搭配转录和AI
                    </p>
                  </div>
                </div>
                {mode === 'separated' && <Check className="w-4 h-4 text-green-300" />}
              </div>
              <div className="text-xs">
                <span className={mode === 'separated' ? 'text-white/70' : 'text-gray-500'}>当前组合</span>
                <div className="font-medium">
                  {transcriptionServices.find(s => s.id === transcriptionService)?.name || 'Deepgram'} + {' '}
                  {aiServices.find(s => s.id === aiService)?.name || 'Groq'}
                </div>
              </div>
            </div>
          </div>

          {/* 分离式服务配置 */}
          {mode === 'separated' && (
            <div className="space-y-6">
              {/* 转录服务 */}
              <div>
                <div className="flex items-center space-x-2 mb-3">
                  <Mic className="w-4 h-4 text-blue-600" />
                  <h2 className="text-sm font-semibold text-gray-900">转录服务</h2>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {transcriptionServices.map((service) => (
                    <div
                      key={service.id}
                      className={`rounded-lg cursor-pointer transition-all duration-200 p-3 ${
                        transcriptionService === service.id
                          ? 'bg-blue-500 text-white shadow-md'
                          : 'bg-white border border-gray-200 hover:border-blue-300'
                      }`}
                      onClick={() => handleServiceChange('transcription', service.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{service.icon}</span>
                          <div>
                            <h3 className="font-medium text-sm">{service.name}</h3>
                            <p className={`text-xs ${
                              transcriptionService === service.id ? 'text-white/80' : 'text-gray-600'
                            }`}>
                              {service.quota}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {service.recommended && (
                            <div className={`px-1.5 py-0.5 rounded text-xs ${
                              transcriptionService === service.id 
                                ? 'bg-yellow-400 text-yellow-900' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              推荐
                            </div>
                          )}
                          {transcriptionService === service.id && (
                            <Check className="w-4 h-4 text-green-300" />
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className={transcriptionService === service.id ? 'text-white/70' : 'text-gray-500'}>
                            延迟
                          </span>
                          <div className="font-medium">{'< ' + service.latency}</div>
                        </div>
                        <div>
                          <span className={transcriptionService === service.id ? 'text-white/70' : 'text-gray-500'}>
                            准确率
                          </span>
                          <div className="font-medium">{service.accuracy}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* AI服务 */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <Brain className="w-4 h-4 text-purple-600" />
                    <h2 className="text-sm font-semibold text-gray-900">AI服务</h2>
                  </div>
                  <button
                    onClick={() => setShowCustomAPIManager(true)}
                    className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    <Plus className="w-3 h-3" />
                    <span>管理自定义API</span>
                  </button>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  {[...aiServices, ...customAPIs].map((service) => (
                    <div
                      key={service.id}
                      className={`rounded-lg cursor-pointer transition-all duration-200 p-3 ${
                        aiService === service.id
                          ? service.icon === '🚀' 
                            ? 'bg-gradient-to-br from-purple-500 to-pink-600 text-white shadow-md'
                            : 'bg-purple-500 text-white shadow-md'
                          : 'bg-white border border-gray-200 hover:border-purple-300'
                      }`}
                      onClick={() => handleServiceChange('ai', service.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">{service.icon || '🚀'}</span>
                          <div>
                            <h3 className="font-medium text-sm">{service.name}</h3>
                            <p className={`text-xs ${
                              aiService === service.id ? 'text-white/80' : 'text-gray-600'
                            }`}>
                              {service.quota || service.description || '自定义API'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          {service.recommended && (
                            <div className={`px-1.5 py-0.5 rounded text-xs ${
                              aiService === service.id 
                                ? 'bg-yellow-400 text-yellow-900' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              推荐
                            </div>
                          )}
                          {service.icon === '🚀' && (
                            <div className={`px-1.5 py-0.5 rounded text-xs ${
                              aiService === service.id 
                                ? 'bg-pink-400 text-pink-900' 
                                : 'bg-pink-100 text-pink-800'
                            }`}>
                              自定义
                            </div>
                          )}
                          {aiService === service.id && (
                            <Check className="w-4 h-4 text-green-300" />
                          )}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className={aiService === service.id ? 'text-white/70' : 'text-gray-500'}>
                            {service.model ? '模型' : '延迟'}
                          </span>
                          <div className="font-medium">
                            {service.model || ('< ' + service.latency)}
                          </div>
                        </div>
                        <div>
                          <span className={aiService === service.id ? 'text-white/70' : 'text-gray-500'}>
                            {service.apiType ? '类型' : '质量'}
                          </span>
                          <div className="font-medium">
                            {service.apiType?.toUpperCase() || service.quality}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 自定义API管理模态框 */}
      {showCustomAPIManager && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">自定义API管理</h3>
              <button
                onClick={() => setShowCustomAPIManager(false)}
                className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
              >
                <X className="w-4 h-4 text-gray-600" />
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
              <div className="text-center py-8 text-gray-500">
                <div className="text-2xl mb-2">🚧</div>
                <p className="text-sm">自定义API管理功能开发中...</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* OCR配置模态框 */}
      {showOCRConfig && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="max-w-4xl w-full">
            <OCRConfigPanel onClose={() => setShowOCRConfig(false)} />
          </div>
        </div>
      )}
    </div>
  )
}

export default CompactServiceConfig
