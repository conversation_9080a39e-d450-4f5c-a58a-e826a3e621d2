import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { ArrowLeft, Save, Building, User, MapPin, Calendar, FileText, Target, Briefcase } from 'lucide-react'

interface PreparationData {
  id: string
  title: string
  company: string
  position: string
  location: string
  interviewDate: string
  interviewType: string
  description: string
  requirements: string[]
  keyPoints: string[]
  questions: string[]
  createdAt: Date
  updatedAt: Date
}

const CreatePreparationPage: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const editData = location.state?.editData

  const [formData, setFormData] = useState<Partial<PreparationData>>({
    title: '',
    company: '',
    position: '',
    location: '',
    interviewDate: '',
    interviewType: '技术面试',
    description: '',
    requirements: [],
    keyPoints: [],
    questions: []
  })

  // 如果是编辑模式，加载现有数据
  useEffect(() => {
    if (editData) {
      setFormData(editData)
    }
  }, [editData])

  const [currentRequirement, setCurrentRequirement] = useState('')
  const [currentKeyPoint, setCurrentKeyPoint] = useState('')
  const [currentQuestion, setCurrentQuestion] = useState('')

  const interviewTypes = [
    '技术面试',
    'HR面试',
    '行为面试',
    '案例面试',
    '群面',
    '终面',
    '其他'
  ]

  const handleInputChange = (field: keyof PreparationData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addRequirement = () => {
    if (currentRequirement.trim()) {
      setFormData(prev => ({
        ...prev,
        requirements: [...(prev.requirements || []), currentRequirement.trim()]
      }))
      setCurrentRequirement('')
    }
  }

  const removeRequirement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements?.filter((_, i) => i !== index) || []
    }))
  }

  const addKeyPoint = () => {
    if (currentKeyPoint.trim()) {
      setFormData(prev => ({
        ...prev,
        keyPoints: [...(prev.keyPoints || []), currentKeyPoint.trim()]
      }))
      setCurrentKeyPoint('')
    }
  }

  const removeKeyPoint = (index: number) => {
    setFormData(prev => ({
      ...prev,
      keyPoints: prev.keyPoints?.filter((_, i) => i !== index) || []
    }))
  }

  const addQuestion = () => {
    if (currentQuestion.trim()) {
      setFormData(prev => ({
        ...prev,
        questions: [...(prev.questions || []), currentQuestion.trim()]
      }))
      setCurrentQuestion('')
    }
  }

  const removeQuestion = (index: number) => {
    setFormData(prev => ({
      ...prev,
      questions: prev.questions?.filter((_, i) => i !== index) || []
    }))
  }

  const handleSave = () => {
    if (!formData.title || !formData.company || !formData.position) {
      alert('请填写必要的信息：标题、公司、职位')
      return
    }

    const preparation: PreparationData = {
      id: editData?.id || Date.now().toString(),
      title: formData.title!,
      company: formData.company!,
      position: formData.position!,
      location: formData.location || '',
      interviewDate: formData.interviewDate || '',
      interviewType: formData.interviewType || '技术面试',
      description: formData.description || '',
      requirements: formData.requirements || [],
      keyPoints: formData.keyPoints || [],
      questions: formData.questions || [],
      createdAt: editData?.createdAt || new Date(),
      updatedAt: new Date()
    }

    // 保存到localStorage
    const existingPreparations = JSON.parse(localStorage.getItem('geekassistant-preparations') || '[]')
    let updatedPreparations

    if (editData) {
      // 编辑模式：更新现有项目
      updatedPreparations = existingPreparations.map((p: PreparationData) =>
        p.id === editData.id ? preparation : p
      )
    } else {
      // 新建模式：添加新项目
      updatedPreparations = [...existingPreparations, preparation]
    }

    localStorage.setItem('geekassistant-preparations', JSON.stringify(updatedPreparations))

    console.log(editData ? '面试准备项已更新:' : '面试准备项已保存:', preparation)
    navigate('/')
  }

  const handleBack = () => {
    navigate('/')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {editData ? '编辑面试准备' : '创建面试准备'}
                </h1>
                <p className="text-sm text-gray-600">
                  {editData ? '修改面试准备项信息' : '为即将到来的面试做好充分准备'}
                </p>
              </div>
            </div>
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>保存</span>
            </button>
          </div>
        </div>
      </div>

      {/* 主内容 */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 基本信息 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Briefcase className="w-5 h-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">基本信息</h2>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  准备项标题 *
                </label>
                <input
                  type="text"
                  value={formData.title || ''}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="例如：字节跳动前端工程师面试"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <Building className="w-4 h-4 inline mr-1" />
                    公司名称 *
                  </label>
                  <input
                    type="text"
                    value={formData.company || ''}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    placeholder="公司名称"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <User className="w-4 h-4 inline mr-1" />
                    职位名称 *
                  </label>
                  <input
                    type="text"
                    value={formData.position || ''}
                    onChange={(e) => handleInputChange('position', e.target.value)}
                    placeholder="职位名称"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    工作地点
                  </label>
                  <input
                    type="text"
                    value={formData.location || ''}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="工作地点"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    面试时间
                  </label>
                  <input
                    type="datetime-local"
                    value={formData.interviewDate || ''}
                    onChange={(e) => handleInputChange('interviewDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  面试类型
                </label>
                <select
                  value={formData.interviewType || '技术面试'}
                  onChange={(e) => handleInputChange('interviewType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {interviewTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <FileText className="w-4 h-4 inline mr-1" />
                  职位描述
                </label>
                <textarea
                  value={formData.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="职位描述、工作内容、团队介绍等..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 准备要点 */}
          <div className="space-y-6">
            {/* 技能要求 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Target className="w-5 h-5 text-green-600" />
                <h2 className="text-lg font-semibold text-gray-900">技能要求</h2>
              </div>
              
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={currentRequirement}
                    onChange={(e) => setCurrentRequirement(e.target.value)}
                    placeholder="添加技能要求..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && addRequirement()}
                  />
                  <button
                    onClick={addRequirement}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    添加
                  </button>
                </div>
                
                <div className="space-y-2">
                  {formData.requirements?.map((req, index) => (
                    <div key={index} className="flex items-center justify-between bg-green-50 px-3 py-2 rounded-lg">
                      <span className="text-sm text-green-800">{req}</span>
                      <button
                        onClick={() => removeRequirement(index)}
                        className="text-green-600 hover:text-red-600 transition-colors"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 重点准备 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Target className="w-5 h-5 text-orange-600" />
                <h2 className="text-lg font-semibold text-gray-900">重点准备</h2>
              </div>
              
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={currentKeyPoint}
                    onChange={(e) => setCurrentKeyPoint(e.target.value)}
                    placeholder="添加重点准备内容..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && addKeyPoint()}
                  />
                  <button
                    onClick={addKeyPoint}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    添加
                  </button>
                </div>
                
                <div className="space-y-2">
                  {formData.keyPoints?.map((point, index) => (
                    <div key={index} className="flex items-center justify-between bg-orange-50 px-3 py-2 rounded-lg">
                      <span className="text-sm text-orange-800">{point}</span>
                      <button
                        onClick={() => removeKeyPoint(index)}
                        className="text-orange-600 hover:text-red-600 transition-colors"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 常见问题 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-4">
                <FileText className="w-5 h-5 text-purple-600" />
                <h2 className="text-lg font-semibold text-gray-900">常见问题</h2>
              </div>
              
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={currentQuestion}
                    onChange={(e) => setCurrentQuestion(e.target.value)}
                    placeholder="添加可能的面试问题..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && addQuestion()}
                  />
                  <button
                    onClick={addQuestion}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    添加
                  </button>
                </div>
                
                <div className="space-y-2">
                  {formData.questions?.map((question, index) => (
                    <div key={index} className="flex items-center justify-between bg-purple-50 px-3 py-2 rounded-lg">
                      <span className="text-sm text-purple-800">{question}</span>
                      <button
                        onClick={() => removeQuestion(index)}
                        className="text-purple-600 hover:text-red-600 transition-colors"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreatePreparationPage
