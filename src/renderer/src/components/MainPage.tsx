import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Play, Settings, User, Shield, Upload, Plus, FileText, Users, Calendar, X, Edit, Trash2 } from 'lucide-react'
import CompactServiceConfig from './CompactServiceConfig'
import ResumeInputModal from './ResumeInputModal'
import CollaborationMode from './CollaborationMode'
import { getLanguageConfig } from '../utils/i18n'

interface MainPageProps {
  onBackToConfig: () => void
  isConfigured: boolean
}

interface PreparationItem {
  id: string
  title: string
  company: string
  position: string
  updatedAt: string
}

const MainPage: React.FC<MainPageProps> = (_props) => {
  const navigate = useNavigate()
  const [hasResume, setHasResume] = useState(false)
  const [preparationItems, setPreparationItems] = useState<PreparationItem[]>([])
  const [isStartingInterview, setIsStartingInterview] = useState(false)
  const [isStartingMeeting, setIsStartingMeeting] = useState(false)
  const [showServiceConfig, setShowServiceConfig] = useState(false)
  const [currentLanguage, setCurrentLanguage] = useState('zh')
  const [currentPage, setCurrentPage] = useState<'main' | 'interview' | 'meeting' | 'resume' | 'permissions' | 'preparation-select'>('main')
  const [collaborationMode, setCollaborationMode] = useState<'interview' | 'meeting'>('interview')
  const [selectedPreparation, setSelectedPreparation] = useState<any>(null)
  const [currentResumeData, setCurrentResumeData] = useState<any>(null)

  // 获取当前语言配置
  const t = getLanguageConfig(currentLanguage)

  useEffect(() => {
    // 加载简历状态和数据
    const resumeData = localStorage.getItem('geekassistant-resumes')
    if (resumeData) {
      const resumes = JSON.parse(resumeData)
      if (resumes.length > 0) {
        setHasResume(true)
        setCurrentResumeData(resumes[0])
      }
    }

    // 加载面试准备项
    loadPreparationItems()
  }, [])

  const loadPreparationItems = () => {
    const stored = localStorage.getItem('geekassistant-preparations')
    if (stored) {
      const items = JSON.parse(stored)
      setPreparationItems(items)
    } else {
      // 默认的准备项
      setPreparationItems([
        {
          id: '1',
          title: 'Java后端工程师',
          company: '字节跳动',
          position: '高级Java开发工程师',
          updatedAt: '2025年7月29日'
        },
        {
          id: '2', 
          title: 'HRBP人力资源业务伙伴',
          company: '腾讯',
          position: 'HRBP',
          updatedAt: '2025年7月29日'
        },
        {
          id: '3',
          title: 'React前端工程师', 
          company: '阿里巴巴',
          position: '前端开发工程师',
          updatedAt: '2025年7月29日'
        },
        {
          id: '4',
          title: '产品经理',
          company: '美团',
          position: '高级产品经理',
          updatedAt: '2025年7月29日'
        },
        {
          id: '5',
          title: 'AI/ML算法工程师',
          company: '百度',
          position: '算法工程师',
          updatedAt: '2025年7月29日'
        }
      ])
    }
  }

  // 修改准备项
  const handleEditPreparation = (item: PreparationItem) => {
    console.log('编辑准备项:', item)
    navigate('/create-preparation', { state: { editData: item } })
  }

  // 删除准备项
  const handleDeletePreparation = (itemId: string) => {
    if (!confirm('确定要删除这个面试准备项吗？此操作无法撤销。')) return
    setPreparationItems(prev => {
      const updated = prev.filter(p => p.id !== itemId)
      localStorage.setItem('geekassistant-preparations', JSON.stringify(updated))
      return updated
    })
  }

  const handleStartInterview = async () => {
    // 跳转到面试准备选择页面
    setCurrentPage('preparation-select')
  }

  const handlePreparationSelected = async (preparation: any) => {
    setSelectedPreparation(preparation)
    setIsStartingInterview(true)

    try {
      // 将选择的面试准备项保存到localStorage，供CollaborationMode使用
      localStorage.setItem('geekassistant-selected-preparation', JSON.stringify(preparation))

      setCollaborationMode('interview')
      setCurrentPage('interview')
      console.log('面试模式启动成功，使用准备项:', preparation?.title || '快速开始')
    } catch (error) {
      console.error('启动面试失败:', error)
    } finally {
      setIsStartingInterview(false)
    }
  }

  const handleStartMeeting = async () => {
    setIsStartingMeeting(true)
    try {
      setCollaborationMode('meeting')
      setCurrentPage('meeting')
      console.log('会议助手启动成功')
    } catch (error) {
      console.error('启动会议助手失败:', error)
    } finally {
      setIsStartingMeeting(false)
    }
  }

  const handleUploadResume = () => {
    // 模拟文件上传
    console.log('上传简历')
    setHasResume(true)
  }

  const handleManualInput = () => {
    // 加载现有简历数据并跳转到简历编辑页面
    const resumes = JSON.parse(localStorage.getItem('geekassistant-resumes') || '[]')
    const existingResume = resumes.length > 0 ? resumes[0] : null
    setCurrentResumeData(existingResume)
    setCurrentPage('resume')
  }

  const handleSaveResume = (resumeData: any) => {
    // 保存简历数据到localStorage
    const resumes = JSON.parse(localStorage.getItem('geekassistant-resumes') || '[]')
    const updatedResumes = resumes.find((r: any) => r.id === resumeData.id)
      ? resumes.map((r: any) => r.id === resumeData.id ? resumeData : r)
      : [...resumes, resumeData]

    localStorage.setItem('geekassistant-resumes', JSON.stringify(updatedResumes))
    setHasResume(true)
    setCurrentResumeData(resumeData)
    setCurrentPage('main') // 保存后返回主页
    console.log('简历已保存:', resumeData)
  }

  const [permissionsData, setPermissionsData] = useState<any>(null)

  const handlePermissions = async () => {
    try {
      const permissions = await window.geekAssistant.checkPermissions()
      console.log('权限状态:', permissions)
      setPermissionsData(permissions)
      setCurrentPage('permissions')
    } catch (error) {
      console.error('检查权限失败:', error)
      setCurrentPage('permissions')
    }
  }

  const handleLanguageSwitch = () => {
    const newLanguage = currentLanguage === 'zh' ? 'en' : 'zh'
    setCurrentLanguage(newLanguage)
    localStorage.setItem('geekassistant-language', newLanguage)
    console.log('语言切换到:', newLanguage === 'zh' ? '中文' : 'English')
  }

  const handleNewPreparation = () => {
    console.log('新建准备项')
    navigate('/create-preparation')
  }

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col">
      {/* 现代化顶部导航栏 - 只在主页面和面试/会议页面显示 */}
      {(currentPage === 'main' || currentPage === 'interview' || currentPage === 'meeting') && (
        <div className="bg-white/70 backdrop-blur-xl border-b border-white/20 px-6 py-4 flex-shrink-0 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {/* 面试/会议模式时显示状态信息 */}
            {(currentPage === 'interview' || currentPage === 'meeting') ? (
              <div className="flex items-center space-x-4">
                {/* 状态指示器和标题 */}
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-3 h-3 rounded-full bg-green-500 shadow-lg" />
                    <div className="absolute inset-0 w-3 h-3 rounded-full bg-green-500 animate-ping opacity-75" />
                  </div>
                  <h1 className="text-lg font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">
                    {currentPage === 'interview' ? t.main.interviewAssistant : t.main.meetingAssistant}
                  </h1>
                </div>

                {/* 连接状态 */}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">•</span>
                  <span className="text-sm font-medium text-green-700">{t.common.connected}</span>
                </div>

                {/* 当前状态 */}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-gray-600">
                    {currentPage === 'interview' ? t.collaboration.interviewReady : t.collaboration.meetingReady}
                  </span>
                </div>

                {/* 服务信息 */}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">•</span>
                  <span className="text-sm font-medium text-gray-700">{t.common.service}:</span>
                  <span className="text-sm font-semibold text-blue-600">Speechmatics + Groq</span>
                </div>
              </div>
            ) : (
              <button
                onClick={handleLanguageSwitch}
                className="px-4 py-2 text-xs font-medium text-slate-600 hover:text-slate-800 bg-white/60 hover:bg-white/80 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md"
              >
                {currentLanguage === 'zh' ? 'EN' : '中文'}
              </button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* 面试/会议模式时显示关闭按钮 */}
            {(currentPage === 'interview' || currentPage === 'meeting') ? (
              <button
                onClick={() => setCurrentPage('main')}
                className="group p-2 hover:bg-white hover:shadow-md rounded-lg transition-all duration-200 border border-transparent hover:border-gray-200"
                title="返回主页"
              >
                <X className="w-4 h-4 text-gray-500 group-hover:text-gray-700" />
              </button>
            ) : (
              <>
                <button
                  onClick={() => setShowServiceConfig(true)}
                  className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-800 bg-white/40 hover:bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md"
                >
                  <Settings className="w-4 h-4" />
                  <span>{t.common.settings}</span>
                </button>

                <button
                  onClick={handlePermissions}
                  className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-800 bg-white/40 hover:bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md"
                >
                  <Shield className="w-4 h-4" />
                  <span>{t.common.permissions}</span>
                </button>

                <button className="flex items-center space-x-2 px-4 py-2 text-slate-600 hover:text-slate-800 bg-white/40 hover:bg-white/60 backdrop-blur-sm border border-white/30 rounded-xl transition-all duration-300 text-xs font-medium shadow-sm hover:shadow-md">
                  <User className="w-4 h-4" />
                  <span>{t.common.account}</span>
                </button>
              </>
            )}
          </div>
        </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="flex-1 overflow-hidden">
        {/* 主页面 */}
        {currentPage === 'main' && (
          <div className="min-h-screen flex flex-col px-3 py-2 overflow-y-auto">
            <div className="max-w-5xl mx-auto w-full">
              {/* 主要功能区域 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                {/* AI面试助手 */}
                <div className="bg-white rounded-lg p-4 border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Play className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{t.main.interviewAssistant}</h3>
                      <p className="text-sm text-gray-600">{t.main.interviewDescription}</p>
                    </div>
                  </div>

                  <button
                    onClick={handleStartInterview}
                    disabled={isStartingInterview}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium text-sm rounded-lg transition-colors disabled:opacity-50"
                  >
                      {isStartingInterview ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>{currentLanguage === 'zh' ? '启动中...' : 'Starting...'}</span>
                        </>
                      ) : (
                        <>
                          <Play className="w-5 h-5" />
                          <span>{t.main.startInterview}</span>
                        </>
                      )}
                  </button>
                </div>

                {/* AI会议助手 */}
                <div className="bg-white rounded-lg p-4 border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all cursor-pointer">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{t.main.meetingAssistant}</h3>
                      <p className="text-sm text-gray-600">{t.main.meetingDescription}</p>
                    </div>
                  </div>

                  <button
                    onClick={handleStartMeeting}
                    disabled={isStartingMeeting}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium text-sm rounded-lg transition-colors disabled:opacity-50"
                  >
                      {isStartingMeeting ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                          <span>{currentLanguage === 'zh' ? '启动中...' : 'Starting...'}</span>
                        </>
                      ) : (
                        <>
                          <Users className="w-5 h-5" />
                          <span>{t.main.startMeeting}</span>
                        </>
                      )}
                  </button>
                </div>
              </div>

              {/* 简历管理区域 */}
              <div className="relative p-4 mb-4 bg-gradient-to-r from-violet-50/50 to-blue-50/50 rounded-xl border border-violet-100/50">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center shadow-lg ${hasResume ? 'bg-gradient-to-br from-violet-500 to-violet-600' : 'bg-gradient-to-br from-slate-400 to-slate-500'} transition-all duration-300`}>
                      <FileText className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-base font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-1">
                        {hasResume ? '个人简历' : '简历管理'}
                      </h3>
                      {hasResume ? (
                        <div className="space-y-1">
                          <p className="text-sm text-slate-700 font-medium">
                            {currentResumeData?.personalInfo?.name || '简历信息'}
                          </p>
                          <p className="text-xs text-slate-600">
                            {currentResumeData?.personalInfo?.email || '个性化面试建议已启用'}
                          </p>
                          <div className="flex items-center space-x-2 mt-2">
                            <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 rounded-full">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              <span className="text-xs font-medium text-green-700">已完善</span>
                            </div>
                            <span className="text-xs text-slate-500">
                              更新于 {currentResumeData?.updatedAt ? new Date(currentResumeData.updatedAt).toLocaleDateString() : '今天'}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-slate-600 font-medium">
                          上传或创建简历，获得精准的面试建议
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2">
                    {!hasResume ? (
                      <>
                        <button
                          onClick={handleUploadResume}
                          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-violet-500 to-violet-600 hover:from-violet-600 hover:to-violet-700 text-white font-medium text-sm rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          <Upload className="w-4 h-4" />
                          <span>上传简历</span>
                        </button>
                        <button
                          onClick={handleManualInput}
                          className="flex items-center space-x-2 px-4 py-2 bg-white hover:bg-gray-50 border border-gray-200 text-slate-700 hover:text-slate-900 font-medium text-sm rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
                        >
                          <FileText className="w-4 h-4" />
                          <span>创建简历</span>
                        </button>
                      </>
                    ) : (
                      <div className="flex space-x-2">
                        <button
                          onClick={handleManualInput}
                          className="flex items-center space-x-2 px-3 py-2 bg-violet-600 hover:bg-violet-700 text-white font-medium text-sm rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
                        >
                          <User className="w-4 h-4" />
                          <span>编辑</span>
                        </button>
                        <button
                          onClick={() => {
                            // 查看简历详情
                            console.log('查看简历详情')
                          }}
                          className="flex items-center space-x-2 px-3 py-2 bg-white hover:bg-gray-50 border border-gray-200 text-slate-700 hover:text-slate-900 font-medium text-sm rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
                        >
                          <FileText className="w-4 h-4" />
                          <span>预览</span>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 面试准备项 */}
              <div className="relative p-3">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-500 rounded-lg flex items-center justify-center shadow-[4px_4px_8px_rgba(31,38,135,0.2)]">
                      <Calendar className="w-4 h-4 text-white" />
                    </div>
                    <h3 className="text-sm font-semibold text-gray-900 truncate">面试准备项</h3>
                  </div>
                  <button
                    onClick={handleNewPreparation}
                    className="flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-medium text-xs rounded-lg shadow-[4px_4px_12px_rgba(245,158,11,0.3)] hover:shadow-[6px_6px_20px_rgba(245,158,11,0.4)] transition-all duration-300 transform hover:scale-105"
                  >
                    <Plus className="w-3 h-3" />
                    <span>新建</span>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  {preparationItems.map((item) => (
                    <div
                      key={item.id}
                      className="group relative bg-white/80 backdrop-blur-sm hover:bg-white/95 border border-white/50 hover:border-white/70 rounded-xl p-2 cursor-pointer transition-all duration-300 shadow-[2px_2px_8px_rgba(31,38,135,0.1)] hover:shadow-[4px_4px_16px_rgba(31,38,135,0.15)] transform hover:scale-[1.02]"
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-amber-50/50 to-orange-50/50 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative">
                        <div className="flex items-start justify-between mb-1">
                          <h4 className="font-medium text-slate-800 text-sm group-hover:text-amber-800 transition-colors">{item.title}</h4>
                          <Calendar className="w-3 h-3 text-slate-400 group-hover:text-amber-500 transition-colors" />
                        </div>
                        <p className="text-xs text-slate-600 mb-1 group-hover:text-amber-700 transition-colors">{item.company}</p>
                        <span className="text-xs text-slate-500 group-hover:text-amber-600 transition-colors">
                          {item.updatedAt}
                        </span>
                      </div>
                      {/* 编辑/删除按钮 */}
                      <div className="absolute top-1 right-1 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => { e.stopPropagation(); handleEditPreparation(item); }}
                          className="p-1 bg-white/80 hover:bg-white text-slate-600 hover:text-blue-600 rounded-full shadow"
                          title="修改"
                        >
                          <Edit className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => { e.stopPropagation(); handleDeletePreparation(item.id); }}
                          className="p-1 bg-white/80 hover:bg-white text-slate-600 hover:text-red-600 rounded-full shadow"
                          title="删除"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 面试页面 */}
        {(currentPage === 'interview' || currentPage === 'meeting') && (
          <CollaborationMode
            mode={collaborationMode}
            onClose={() => setCurrentPage('main')}
            isEmbedded={true}
            language={currentLanguage}
          />
        )}

        {/* 简历编辑页面 */}
        {currentPage === 'resume' && (
          <ResumeInputModal
            isOpen={true}
            onClose={() => setCurrentPage('main')}
            onSave={handleSaveResume}
            initialData={currentResumeData}
            isEmbedded={true}
            currentLanguage={currentLanguage}
            onLanguageChange={setCurrentLanguage}
          />
        )}

        {/* 面试准备选择页面 */}
        {currentPage === 'preparation-select' && (
          <div className="h-full overflow-hidden p-4">
            <div className="max-w-6xl mx-auto h-full flex flex-col">
              {/* 顶部导航栏 */}
              <div className="flex items-center justify-between mb-2">
                <button
                  onClick={() => setCurrentPage('main')}
                  className="flex items-center space-x-2 text-gray-600 hover:text-black transition-colors"
                >
                  <span>←</span>
                  <span className="font-medium">{t.common.back}</span>
                </button>

                <button
                  onClick={() => setCurrentLanguage(currentLanguage === 'zh' ? 'en' : 'zh')}
                  className="px-3 py-1 text-sm text-gray-600 hover:text-black border border-gray-300 rounded-md transition-colors"
                >
                  {currentLanguage === 'zh' ? 'EN' : '中文'}
                </button>
              </div>

              {/* 页面标题 */}
              <div className="text-center mb-6">
                <h1 className="text-xl font-bold text-black">选择面试准备项</h1>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 flex-1 overflow-y-auto">
                {/* 快速开始卡片 */}
                <div
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all bg-gradient-to-r from-green-50 to-blue-50 border-green-200 hover:border-green-300 hover:from-green-100 hover:to-blue-100 ${
                    selectedPreparation?.id === 'quick-start'
                      ? 'border-green-500 bg-gradient-to-r from-green-100 to-blue-100 ring-2 ring-green-200'
                      : ''
                  }`}
                  onClick={() => setSelectedPreparation({
                    id: 'quick-start',
                    title: '🚀 快速开始',
                    company: '通用',
                    position: '通用职位',
                    location: '',
                    interviewDate: '',
                    interviewType: '技术面试',
                    description: '快速开始面试，使用通用配置',
                    requirements: ['基础技能', '沟通能力', '问题解决能力'],
                    keyPoints: ['保持自信', '清晰表达', '展示技能'],
                    questions: [],
                    createdAt: new Date(),
                    updatedAt: new Date()
                  })}
                >
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <Play className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-gray-900 truncate">🚀 快速开始</h3>
                      <p className="text-xs text-gray-600">通用配置</p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>• 通用技术面试场景</p>
                    <p>• 即开即用，无需配置</p>
                  </div>
                </div>

                {/* 现有面试准备项 */}
                {preparationItems.map((preparation) => (
                  <div
                    key={preparation.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedPreparation?.id === preparation.id
                        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedPreparation(preparation)}
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <FileText className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-semibold text-gray-900 truncate">{preparation.title}</h3>
                        <p className="text-xs text-gray-600">{preparation.company} - {preparation.position}</p>
                      </div>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            navigate('/create-preparation', { state: { editData: preparation } })
                          }}
                          className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
                          title="编辑"
                        >
                          <Edit className="w-3.5 h-3.5" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeletePreparation(preparation.id)
                          }}
                          className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors"
                          title="删除"
                        >
                          <Trash2 className="w-3.5 h-3.5" />
                        </button>
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>• {preparation.interviewType || '技术面试'}</p>
                      <p>• 更新时间：{preparation.updatedAt || '最近'}</p>
                    </div>
                  </div>
                ))}

                {/* 新建准备项卡片 */}
                <div
                  className="p-4 rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 cursor-pointer transition-all hover:bg-gray-50"
                  onClick={() => {
                    // 这里可以添加创建新准备项的逻辑
                    console.log('创建新的面试准备项')
                  }}
                >
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <Plus className="w-5 h-5 text-gray-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-gray-700 truncate">创建新准备项</h3>
                      <p className="text-xs text-gray-500">定制化面试准备</p>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>• 特定公司职位配置</p>
                    <p>• 个性化面试策略</p>
                  </div>
                </div>
              </div>

              {/* 紧凑底部操作区 */}
              <div className="mt-4 flex items-center justify-between border-t border-gray-200 pt-4">
                <div className="text-sm text-gray-600">
                  {selectedPreparation ? (
                    <span>✓ {selectedPreparation.title}</span>
                  ) : (
                    <span>请选择面试准备项</span>
                  )}
                </div>
                <button
                  onClick={() => selectedPreparation && handlePreparationSelected(selectedPreparation)}
                  disabled={!selectedPreparation || isStartingInterview}
                  className={`flex items-center space-x-2 px-6 py-2 rounded-lg transition-all font-medium text-sm ${
                    selectedPreparation && !isStartingInterview
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {isStartingInterview ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>启动中...</span>
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      <span>开始面试</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 权限管理页面 */}
        {currentPage === 'permissions' && (
          <div className="h-full overflow-hidden p-4">
            <div className="max-w-5xl mx-auto h-full flex flex-col">
              {/* 顶部导航栏 */}
              <div className="flex items-center justify-between mb-2">
                <button
                  onClick={() => setCurrentPage('main')}
                  className="flex items-center space-x-2 text-gray-600 hover:text-black transition-colors"
                >
                  <span>←</span>
                  <span className="font-medium">{t.common.back}</span>
                </button>

                <div className="flex items-center space-x-3">
                  <button
                    onClick={async () => {
                      try {
                        const permissions = await window.geekAssistant.checkPermissions()
                        setPermissionsData(permissions)
                      } catch (error) {
                        console.error('刷新权限状态失败:', error)
                      }
                    }}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
                  >
                    <span>🔄</span>
                    <span>{t.permissions.refresh}</span>
                  </button>

                  <button
                    onClick={() => setCurrentLanguage(currentLanguage === 'zh' ? 'en' : 'zh')}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-black border border-gray-300 rounded-md transition-colors"
                  >
                    {currentLanguage === 'zh' ? 'EN' : '中文'}
                  </button>
                </div>
              </div>

              {/* 页面标题 */}
              <div className="text-center mb-4">
                <h1 className="text-xl font-bold text-black">{t.permissions.title}</h1>
              </div>

              {/* 紧凑权限卡片网格 */}
              <div className="grid grid-cols-2 gap-3 flex-1">
                {/* 屏幕录制权限 */}
                <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      permissionsData?.screenRecording?.granted
                        ? 'bg-green-100'
                        : 'bg-red-100'
                    }`}>
                      <span className="text-sm">🖥️</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-black truncate">{t.permissions.screenRecording}</h3>
                      <p className="text-xs text-gray-600">{t.permissions.screenRecordingDesc}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      permissionsData?.screenRecording?.granted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {permissionsData?.screenRecording?.granted ? t.permissions.granted : t.permissions.denied}
                    </span>
                  </div>

                  {!permissionsData?.screenRecording?.granted && (
                    <button
                      onClick={async () => {
                        try {
                          await window.geekAssistant.openSystemPreferences('screen-recording')
                        } catch (error) {
                          console.error('打开系统设置失败:', error)
                        }
                      }}
                      className="w-full px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs font-medium transition-colors"
                    >
                      {t.permissions.openSettings}
                    </button>
                  )}
                </div>

                {/* 麦克风权限 */}
                <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      permissionsData?.microphone?.granted
                        ? 'bg-green-100'
                        : 'bg-red-100'
                    }`}>
                      <span className="text-sm">🎤</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-black truncate">{t.permissions.microphone}</h3>
                      <p className="text-xs text-gray-600">{t.permissions.microphoneDesc}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      permissionsData?.microphone?.granted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {permissionsData?.microphone?.granted ? t.permissions.granted : t.permissions.denied}
                    </span>
                  </div>

                  {!permissionsData?.microphone?.granted && (
                    <div className="flex space-x-1">
                      {permissionsData?.microphone?.canRequest && (
                        <button
                          onClick={async () => {
                            try {
                              const result = await window.geekAssistant.requestMicrophonePermission()
                              if (result.granted) {
                                const permissions = await window.geekAssistant.checkPermissions()
                                setPermissionsData(permissions)
                              }
                            } catch (error) {
                              console.error('请求麦克风权限失败:', error)
                            }
                          }}
                          className="flex-1 px-2 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded text-xs font-medium transition-colors"
                        >
                          {t.permissions.requestPermission}
                        </button>
                      )}
                      <button
                        onClick={async () => {
                          try {
                            await window.geekAssistant.openSystemPreferences('microphone')
                          } catch (error) {
                            console.error('打开系统设置失败:', error)
                          }
                        }}
                        className="flex-1 px-2 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs font-medium transition-colors"
                      >
                        {t.permissions.openSettings}
                      </button>
                    </div>
                  )}
                </div>

                {/* API密钥状态 */}
                <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      permissionsData?.apiKey?.granted
                        ? 'bg-green-100'
                        : 'bg-yellow-100'
                    }`}>
                      <span className="text-sm">🔑</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-black truncate">{t.permissions.apiKey}</h3>
                      <p className="text-xs text-gray-600">{t.permissions.apiKeyDesc}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      permissionsData?.apiKey?.granted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {permissionsData?.apiKey?.granted ? t.permissions.configured : t.permissions.notConfigured}
                    </span>
                  </div>

                  {!permissionsData?.apiKey?.granted && (
                    <button
                      onClick={() => setShowServiceConfig(true)}
                      className="w-full px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs font-medium transition-colors"
                    >
                      {t.permissions.configureAPI}
                    </button>
                  )}
                </div>

                {/* 音频设备状态 */}
                <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      permissionsData?.audioDevice?.granted
                        ? 'bg-green-100'
                        : 'bg-red-100'
                    }`}>
                      <span className="text-sm">🔊</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-black truncate">{t.permissions.audioDevice}</h3>
                      <p className="text-xs text-gray-600">{t.permissions.audioDeviceDesc}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      permissionsData?.audioDevice?.granted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {permissionsData?.audioDevice?.granted ? t.permissions.normal : t.permissions.abnormal}
                    </span>
                  </div>

                  <div className="flex space-x-1">
                    <button
                      onClick={async () => {
                        try {
                          const result = await window.geekAssistant.testAudioCapture()
                          console.log('音频测试结果:', result)
                        } catch (error) {
                          console.error('音频测试失败:', error)
                        }
                      }}
                      className="flex-1 px-2 py-1.5 bg-green-600 hover:bg-green-700 text-white rounded text-xs font-medium transition-colors"
                    >
                      {t.permissions.test}
                    </button>
                    {!permissionsData?.audioDevice?.granted && (
                      <button
                        onClick={async () => {
                          try {
                            await window.geekAssistant.openSystemPreferences('screen-recording')
                          } catch (error) {
                            console.error('打开系统设置失败:', error)
                          }
                        }}
                        className="flex-1 px-2 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs font-medium transition-colors"
                      >
                        {t.permissions.settings}
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* 简化权限说明 */}
              <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <span className="text-sm">💡</span>
                  <p className="text-xs text-blue-800">
                    <strong>{t.permissions.tip}</strong>{t.permissions.tipMessage}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 服务配置模态框 */}
      {showServiceConfig && (
        <CompactServiceConfig onClose={() => setShowServiceConfig(false)} />
      )}
    </div>
  )
}

export default MainPage
