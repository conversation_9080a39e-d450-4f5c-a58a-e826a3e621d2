import React, { useState, useEffect } from 'react';
import { Eye, Cloud, HardDrive, CheckCircle, XCircle, Refresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap, Bot } from 'lucide-react';

interface OCRStatus {
  success: boolean;
  currentMode: 'local-ocr' | 'remote-api' | 'groq-vision' | 'zhipu-vision';
  localOCRAvailable: boolean;
  remoteAPIAvailable: boolean;
  groqVisionAvailable: boolean;
  zhipuVisionAvailable: boolean;
  remoteAPIStatus?: {
    available: boolean;
    accountsStatus: {
      totalAccounts: number;
      currentAccountIndex: number;
      currentAccount: string | null;
      accountsFilePath: string;
      fileExists: boolean;
    };
  };
  groqVisionStatus?: {
    available: boolean;
    model: string;
  };
  zhipuVisionStatus?: {
    available: boolean;
    model: string;
    apiKey: string;
  };
}

interface OCRConfigPanelProps {
  onClose?: () => void;
}

const OCRConfigPanel: React.FC<OCRConfigPanelProps> = ({ onClose }) => {
  const [ocrStatus, setOcrStatus] = useState<OCRStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'local-ocr' | 'remote-api' | 'groq-vision' | 'zhipu-vision'>('local-ocr');

  // 加载OCR状态
  const loadOCRStatus = async () => {
    setIsLoading(true);
    try {
      const status = await window.geekAssistant.getOCRStatus();
      console.log('🔄 OCR Status loaded:', status);
      setOcrStatus(status);
      if (status.success) {
        setSelectedMode(status.currentMode);
      }
    } catch (error) {
      console.error('Failed to load OCR status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 保存OCR模式配置
  const saveOCRMode = async (mode: 'local-ocr' | 'remote-api' | 'groq-vision' | 'zhipu-vision') => {
    try {
      // 获取当前配置
      const currentConfig = await window.geekAssistant.getCurrentServiceConfig();
      
      // 更新OCR模式
      const updatedConfig = {
        ...currentConfig,
        ocrMode: mode,
        reverseAnalysis: {
          ...currentConfig.reverseAnalysis,
          enabled: mode === 'remote-api'
        }
      };

      console.log('🔧 Saving OCR config:', updatedConfig);
      const success = await window.geekAssistant.updateServiceConfig(updatedConfig);
      
      if (success) {
        setSelectedMode(mode);
        // 重新加载状态
        await loadOCRStatus();
        console.log('✅ OCR mode saved successfully');

        // 保存成功后关闭弹窗
        if (onClose) {
          onClose();
        }
      } else {
        console.error('❌ Failed to save OCR mode');
        alert('保存失败，请重试');
      }
    } catch (error) {
      console.error('❌ Error saving OCR mode:', error);
      alert('保存过程中发生错误：' + error.message);
    }
  };

  useEffect(() => {
    loadOCRStatus();
  }, []);

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mr-2" />
          <span className="text-gray-600">加载OCR配置...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 max-w-lg mx-auto">
      {/* 标题 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Eye className="w-5 h-5 text-blue-500" />
          <h2 className="text-lg font-semibold text-gray-800">OCR 识别模式</h2>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            ✕
          </button>
        )}
      </div>

      {/* 当前状态 - 简化版 */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">当前模式:</span>
          <span className={`font-medium ${
            ocrStatus?.currentMode === 'remote-api' ? 'text-blue-600' :
            ocrStatus?.currentMode === 'groq-vision' ? 'text-purple-600' :
            ocrStatus?.currentMode === 'zhipu-vision' ? 'text-orange-600' : 'text-green-600'
          }`}>
            {ocrStatus?.currentMode === 'remote-api' ? '远程API' :
             ocrStatus?.currentMode === 'groq-vision' ? 'Groq Vision' :
             ocrStatus?.currentMode === 'zhipu-vision' ? '智谱AI Vision' : '本地OCR'}
          </span>
        </div>
        {ocrStatus?.currentMode === 'remote-api' && ocrStatus?.remoteAPIStatus && (
          <div className="mt-2 text-xs text-gray-500">
            可用账号: {ocrStatus.remoteAPIStatus.accountsStatus?.totalAccounts || 0}
          </div>
        )}
      </div>

      {/* 模式选择 - 简化版 */}
      <div className="space-y-3 mb-4">
        {/* 本地OCR模式 */}
        <div
          className={`p-3 rounded-lg border cursor-pointer transition-all ${
            selectedMode === 'local-ocr'
              ? 'border-green-500 bg-green-50'
              : 'border-gray-200 hover:border-gray-300'
          }`}
          onClick={() => setSelectedMode('local-ocr')}
        >
          <div className="flex items-center space-x-3">
            <HardDrive className={`w-5 h-5 ${
              selectedMode === 'local-ocr' ? 'text-green-600' : 'text-gray-400'
            }`} />
            <div className="flex-1">
              <h4 className="font-medium text-gray-800">本地OCR识别</h4>
              <p className="text-xs text-gray-600">
                离线识别 + AI分析，无需网络
              </p>
            </div>
            {selectedMode === 'local-ocr' && (
              <CheckCircle className="w-4 h-4 text-green-500" />
            )}
          </div>
        </div>

        {/* 远程API模式 */}
        <div
          className={`p-3 rounded-lg border cursor-pointer transition-all ${
            selectedMode === 'remote-api'
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-200 hover:border-gray-300'
          } ${!ocrStatus?.remoteAPIAvailable ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => ocrStatus?.remoteAPIAvailable && setSelectedMode('remote-api')}
        >
          <div className="flex items-center space-x-3">
            <Cloud className={`w-5 h-5 ${
              selectedMode === 'remote-api' ? 'text-blue-600' : 'text-gray-400'
            }`} />
            <div className="flex-1">
              <h4 className="font-medium text-gray-800">远程API识别</h4>
              <p className="text-xs text-gray-600">
                GPT-4V直接分析，更高准确率
                {!ocrStatus?.remoteAPIAvailable && ' (不可用)'}
              </p>
            </div>
            {selectedMode === 'remote-api' && ocrStatus?.remoteAPIAvailable && (
              <CheckCircle className="w-4 h-4 text-blue-500" />
            )}
          </div>
        </div>

        {/* Groq Vision模式 */}
        <div
          className={`p-3 rounded-lg border cursor-pointer transition-all ${
            selectedMode === 'groq-vision'
              ? 'border-purple-500 bg-purple-50'
              : 'border-gray-200 hover:border-gray-300'
          } ${!ocrStatus?.groqVisionAvailable ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => ocrStatus?.groqVisionAvailable && setSelectedMode('groq-vision')}
        >
          <div className="flex items-center space-x-3">
            <Zap className={`w-5 h-5 ${
              selectedMode === 'groq-vision' ? 'text-purple-600' : 'text-gray-400'
            }`} />
            <div className="flex-1">
              <h4 className="font-medium text-gray-800">Groq Vision识别</h4>
              <p className="text-xs text-gray-600">
                Llama 4 Scout超快推理，使用你的Groq API
                {!ocrStatus?.groqVisionAvailable && ' (需要Groq API密钥)'}
              </p>
            </div>
            {selectedMode === 'groq-vision' && ocrStatus?.groqVisionAvailable && (
              <CheckCircle className="w-4 h-4 text-purple-500" />
            )}
          </div>
        </div>

        {/* 智谱AI Vision模式 */}
        <div
          className={`p-3 rounded-lg border cursor-pointer transition-all ${
            selectedMode === 'zhipu-vision'
              ? 'border-orange-500 bg-orange-50'
              : 'border-gray-200 hover:border-gray-300'
          } ${!ocrStatus?.zhipuVisionAvailable ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={() => ocrStatus?.zhipuVisionAvailable && setSelectedMode('zhipu-vision')}
        >
          <div className="flex items-center space-x-3">
            <Bot className={`w-5 h-5 ${
              selectedMode === 'zhipu-vision' ? 'text-orange-600' : 'text-gray-400'
            }`} />
            <div className="flex-1">
              <h4 className="font-medium text-gray-800">智谱AI Vision识别</h4>
              <p className="text-xs text-gray-600">
                GLM-4V-Plus免费模型，内置API密钥
                {!ocrStatus?.zhipuVisionAvailable && ' (服务不可用)'}
              </p>
            </div>
            {selectedMode === 'zhipu-vision' && ocrStatus?.zhipuVisionAvailable && (
              <CheckCircle className="w-4 h-4 text-orange-500" />
            )}
          </div>
        </div>
      </div>

      {/* 操作按钮 - 简化版 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button
            onClick={loadOCRStatus}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            title="刷新状态"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
          <button
            onClick={async () => {
              try {
                console.log('🧪 Starting API diagnosis...');
                const diagnosis = await window.geekAssistant.diagnoseRemoteAPI();
                console.log('🧪 Diagnosis result:', diagnosis);
                alert(`API诊断结果:\n总账号: ${diagnosis.totalAccounts}\n可用账号: ${diagnosis.validAccounts}\n详细信息请查看控制台`);
              } catch (error) {
                console.error('Diagnosis failed:', error);
                alert('诊断失败: ' + error.message);
              }
            }}
            className="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 transition-colors"
            title="诊断API问题"
          >
            诊断
          </button>
        </div>

        <div className="flex items-center space-x-2">
          {onClose && (
            <button
              onClick={onClose}
              className="px-3 py-2 text-gray-600 hover:text-gray-800 transition-colors text-sm"
            >
              取消
            </button>
          )}
          <button
            onClick={() => saveOCRMode(selectedMode)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

export default OCRConfigPanel;
