import React, { useState, useEffect } from 'react';
import { Shield, Eye, EyeOff, Zap, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface StealthStatus {
  mainWindow: {
    level: string;
    isHidden: boolean;
  };
  floatingWindow: {
    level: string;
    isHidden: boolean;
  };
  isInitialized: boolean;
}

interface PerformanceReport {
  current: {
    memoryUsage: {
      heapUsed: number;
    };
    cpuUsage: number;
    stealthOverhead: number;
  };
  recommendations: string[];
}

interface SecurityReport {
  summary: {
    total: number;
    passed: number;
    warnings: number;
    failed: number;
  };
  recommendations: string[];
}

const StealthStatusPanel: React.FC = () => {
  const [stealthStatus, setStealthStatus] = useState<StealthStatus | null>(null);
  const [performanceReport, setPerformanceReport] = useState<PerformanceReport | null>(null);
  const [securityReport, setSecurityReport] = useState<SecurityReport | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isClickThroughEnabled, setIsClickThroughEnabled] = useState(false);
  const [isContentProtectionEnabled, setIsContentProtectionEnabled] = useState(true);

  useEffect(() => {
    const updateStatus = async () => {
      try {
        const [stealth, performance, security] = await Promise.all([
          window.geekAssistant.getStealthStatus(),
          window.geekAssistant.getPerformanceReport(),
          window.geekAssistant.getSecurityReport()
        ]);

        setStealthStatus(stealth);
        setPerformanceReport(performance);
        setSecurityReport(security);
      } catch (error) {
        console.error('Failed to update stealth status:', error);
      }
    };

    // 监听鼠标穿透状态变化 - 解决快捷键切换后界面不更新的问题
    const handleMousePassthroughChange = (enabled: boolean) => {
      setIsClickThroughEnabled(enabled);
      console.log(`🖱️ Mouse passthrough state changed via shortcut: ${enabled}`);
    };

    // 添加事件监听器
    const removeListener = window.geekAssistant?.onMousePassthroughChanged?.(handleMousePassthroughChange);

    // 初始加载
    updateStatus();

    // 定期更新
    const interval = setInterval(updateStatus, 5000);

    return () => {
      clearInterval(interval);
      // 清理事件监听器
      if (removeListener && typeof removeListener === 'function') {
        removeListener();
      }
    };
  }, []);

  const getStatusIcon = (level: string) => {
    switch (level) {
      case 'NORMAL':
        return <Eye className="w-4 h-4 text-blue-500" />;
      case 'BASIC':
        return <EyeOff className="w-4 h-4 text-yellow-500" />;
      case 'ENHANCED':
        return <Shield className="w-4 h-4 text-orange-500" />;
      case 'SUPER':
        return <Shield className="w-4 h-4 text-red-500" />;
      default:
        return <Eye className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPerformanceColor = (overhead: number) => {
    if (overhead < 10) return 'text-green-500';
    if (overhead < 20) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getSecurityColor = (report: SecurityReport) => {
    if (report.summary.failed > 0) return 'text-red-500';
    if (report.summary.warnings > 0) return 'text-yellow-500';
    return 'text-green-500';
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 flex flex-col items-end space-y-2 z-50">
        {/* 鼠标穿透切换按钮 */}
        <button
          onClick={async () => {
            try {
              if (isClickThroughEnabled) {
                // 禁用鼠标穿透
                const result = await window.geekAssistant.disableClickThrough();
                if (result.success) {
                  setIsClickThroughEnabled(false);
                  console.log('🖱️ Click-through disabled via icon');
                }
              } else {
                // 启用鼠标穿透
                const result = await window.geekAssistant.enableClickThrough();
                if (result.success) {
                  setIsClickThroughEnabled(true);
                  console.log('🖱️ Click-through enabled via icon');
                }
              }
            } catch (error) {
              console.error('Failed to toggle click-through:', error);
            }
          }}
          className={`p-2 ${
            isClickThroughEnabled
              ? 'bg-red-600 hover:bg-red-700'
              : 'bg-gray-800 hover:bg-gray-700'
          } text-white rounded-full shadow-lg transition-colors`}
          title={isClickThroughEnabled ? '🔴 鼠标穿透已启用 - 点击禁用' : '🖱️ 鼠标穿透已禁用 - 点击启用'}
        >
          {isClickThroughEnabled ? <span className="w-5 h-5 flex items-center justify-center text-lg">🔴</span> : <span className="w-5 h-5 flex items-center justify-center text-lg">🖱️</span>}
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <Shield className="w-5 h-5 text-gray-700" />
          <span className="font-medium text-gray-700">隐身状态监控</span>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ×
        </button>
      </div>

      <div className="p-3 space-y-3 max-h-96 overflow-y-auto">
        {/* 隐身状态 */}
        {stealthStatus && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-1">
              <Shield className="w-4 h-4" />
              <span>隐身状态</span>
            </h4>
            
            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span>主窗口</span>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(stealthStatus.mainWindow.level)}
                  <span className="text-gray-600">{stealthStatus.mainWindow.level}</span>
                  {stealthStatus.mainWindow.isHidden && (
                    <span className="text-red-500">隐藏</span>
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span>浮动窗口</span>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(stealthStatus.floatingWindow.level)}
                  <span className="text-gray-600">{stealthStatus.floatingWindow.level}</span>
                  {stealthStatus.floatingWindow.isHidden && (
                    <span className="text-red-500">隐藏</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 性能状态 */}
        {performanceReport && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-1">
              <Zap className="w-4 h-4" />
              <span>性能状态</span>
            </h4>
            
            <div className="space-y-1 text-xs">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span>内存使用</span>
                <span className="text-gray-600">
                  {(performanceReport.current.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB
                </span>
              </div>
              
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span>CPU使用</span>
                <span className="text-gray-600">
                  {performanceReport.current.cpuUsage.toFixed(1)}%
                </span>
              </div>
              
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span>隐身开销</span>
                <span className={getPerformanceColor(performanceReport.current.stealthOverhead)}>
                  {performanceReport.current.stealthOverhead.toFixed(1)}%
                </span>
              </div>
            </div>

            {performanceReport.recommendations.length > 0 && (
              <div className="mt-2">
                <div className="text-xs text-gray-600 mb-1">建议:</div>
                {performanceReport.recommendations.slice(0, 2).map((rec, index) => (
                  <div key={index} className="text-xs text-yellow-600 bg-yellow-50 p-1 rounded mb-1">
                    {rec}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 安全状态 */}
        {securityReport && (
          <div className="space-y-2">
            <h4 className={`text-sm font-medium flex items-center space-x-1 ${getSecurityColor(securityReport)}`}>
              <Shield className="w-4 h-4" />
              <span>安全状态</span>
            </h4>
            
            <div className="grid grid-cols-3 gap-1 text-xs">
              <div className="flex items-center space-x-1 p-1 bg-green-50 rounded">
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span className="text-green-700">{securityReport.summary.passed}</span>
              </div>
              
              <div className="flex items-center space-x-1 p-1 bg-yellow-50 rounded">
                <AlertTriangle className="w-3 h-3 text-yellow-500" />
                <span className="text-yellow-700">{securityReport.summary.warnings}</span>
              </div>
              
              <div className="flex items-center space-x-1 p-1 bg-red-50 rounded">
                <XCircle className="w-3 h-3 text-red-500" />
                <span className="text-red-700">{securityReport.summary.failed}</span>
              </div>
            </div>

            {securityReport.recommendations.length > 0 && (
              <div className="mt-2">
                <div className="text-xs text-gray-600 mb-1">安全建议:</div>
                {securityReport.recommendations.slice(0, 2).map((rec, index) => (
                  <div key={index} className="text-xs text-red-600 bg-red-50 p-1 rounded mb-1">
                    {rec}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* reverse-analysis鼠标穿透控制 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-1">
            <span>🖱️</span>
            <span>鼠标穿透</span>
          </h4>
          <div className="flex space-x-2">
            <button
              onClick={async () => {
                try {
                  const result = await window.geekAssistant.enableClickThrough();
                  if (result.success) {
                    setIsClickThroughEnabled(true);
                    console.log('🖱️ Click-through enabled');
                  }
                } catch (error) {
                  console.error('Failed to enable click-through:', error);
                }
              }}
              className={`px-2 py-1 text-xs rounded ${
                isClickThroughEnabled
                  ? 'bg-red-100 text-red-700'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
              disabled={isClickThroughEnabled}
            >
              启用穿透
            </button>
            <button
              onClick={async () => {
                try {
                  const result = await window.geekAssistant.disableClickThrough();
                  if (result.success) {
                    setIsClickThroughEnabled(false);
                    console.log('🖱️ Click-through disabled');
                  }
                } catch (error) {
                  console.error('Failed to disable click-through:', error);
                }
              }}
              className={`px-2 py-1 text-xs rounded ${
                !isClickThroughEnabled
                  ? 'bg-red-100 text-red-700'
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
              }`}
              disabled={!isClickThroughEnabled}
            >
              禁用穿透
            </button>
          </div>
          <div className="text-xs text-gray-600">
            {isClickThroughEnabled ? '🔴 鼠标点击会穿透窗口' : '🟢 窗口可正常交互'}
          </div>
        </div>

        {/* reverse-analysis屏幕共享保护控制 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-1">
            <span>🛡️</span>
            <span>屏幕共享保护</span>
          </h4>
          <div className="flex space-x-2">
            <button
              onClick={async () => {
                try {
                  const result = await window.geekAssistant.enableContentProtection();
                  if (result.success) {
                    setIsContentProtectionEnabled(true);
                    console.log('🛡️ Content protection enabled');
                  }
                } catch (error) {
                  console.error('Failed to enable content protection:', error);
                }
              }}
              className={`px-2 py-1 text-xs rounded ${
                isContentProtectionEnabled
                  ? 'bg-red-100 text-red-700'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
              disabled={isContentProtectionEnabled}
            >
              启用保护
            </button>
            <button
              onClick={async () => {
                try {
                  const result = await window.geekAssistant.disableContentProtection();
                  if (result.success) {
                    setIsContentProtectionEnabled(false);
                    console.log('🛡️ Content protection disabled');
                  }
                } catch (error) {
                  console.error('Failed to disable content protection:', error);
                }
              }}
              className={`px-2 py-1 text-xs rounded ${
                !isContentProtectionEnabled
                  ? 'bg-red-100 text-red-700'
                  : 'bg-orange-100 text-orange-700 hover:bg-orange-200'
              }`}
              disabled={!isContentProtectionEnabled}
            >
              禁用保护
            </button>
          </div>
          <div className="text-xs text-gray-600">
            {isContentProtectionEnabled ? '🛡️ 屏幕共享被阻止' : '⚠️ 屏幕共享可被检测'}
          </div>
        </div>

        {/* 🧪 截图识别测试 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-1">
            <span>🧪</span>
            <span>截图识别测试</span>
          </h4>
          <div className="flex space-x-2">
            <button
              onClick={async () => {
                try {
                  console.log('🔍 Starting local OCR test...');
                  const result = await window.geekAssistant.testLocalOCR();
                  console.log('🔍 Local OCR test result:', result);

                  if (result.success) {
                    alert(`✅ 本地OCR全屏测试成功！\n方法: ${result.method}\n提取文字: ${result.ocrText?.substring(0, 50)}...\n分析结果: ${result.response?.substring(0, 100)}...`);
                  } else {
                    alert(`❌ 本地OCR全屏测试失败: ${result.error}`);
                  }
                } catch (error: unknown) {
                  console.error('Failed to test local OCR:', error);
                  const message = error instanceof Error ? error.message : String(error);
                  alert(`❌ 本地OCR全屏测试异常: ${message}`);
                }
              }}
              className="px-2 py-1 text-xs rounded bg-blue-100 text-blue-700 hover:bg-blue-200"
            >
              📸 全屏OCR (Cmd+Shift+C)
            </button>
            <button
              onClick={async () => {
                try {
                  console.log('🎯 Starting local OCR area test...');
                  const result = await window.geekAssistant.testLocalOCRArea();
                  console.log('🎯 Local OCR area test result:', result);

                  if (result.success) {
                    alert(`✅ 本地OCR区域测试成功！\n方法: ${result.method}\n提取文字: ${result.ocrText?.substring(0, 50)}...\n分析结果: ${result.response?.substring(0, 100)}...`);
                  } else {
                    alert(`❌ 本地OCR区域测试失败: ${result.error}`);
                  }
                } catch (error: unknown) {
                  console.error('Failed to test local OCR area:', error);
                  const message = error instanceof Error ? error.message : String(error);
                  alert(`❌ 本地OCR区域测试异常: ${message}`);
                }
              }}
              className="px-2 py-1 text-xs rounded bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
            >
              🎯 区域OCR (Cmd+Shift+A)
            </button>
          </div>
          <div className="text-xs text-gray-600 mt-2">
            本地OCR + Groq AI分析 | 支持快捷键操作
          </div>
        </div>

        {/* 快捷键提示 */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">快捷键</h4>
          <div className="grid grid-cols-2 gap-1 text-xs">
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-mono">⌘+H</span> 主窗口
            </div>
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-mono">⌘+F</span> 浮动窗口
            </div>
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-mono">⌘+B</span> 整体隐藏
            </div>
            <div className="p-1 bg-gray-50 rounded">
              <span className="font-mono">⌘+S</span> 超级隐身
            </div>
            <div className="p-1 bg-red-50 rounded border border-red-200">
              <span className="font-mono text-red-700">⌘+M</span> <span className="text-red-700">鼠标穿透</span>
            </div>
            <div className="p-1 bg-blue-50 rounded text-xs text-blue-600">
              点击盾牌也可切换穿透
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StealthStatusPanel;
