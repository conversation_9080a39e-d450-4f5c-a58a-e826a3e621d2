// 国际化配置
export interface LanguageConfig {
  // 通用
  common: {
    settings: string
    permissions: string
    account: string
    close: string
    back: string
    connected: string
    disconnected: string
    service: string
  }
  
  // 主页
  main: {
    interviewAssistant: string
    interviewDescription: string
    startInterview: string
    meetingAssistant: string
    meetingDescription: string
    startMeeting: string
    resumeManagement: string
    uploadResume: string
    manualInput: string
    interviewPreparation: string
    newPreparation: string
  }
  
  // 面试/会议页面
  collaboration: {
    aiSuggestions: string
    interviewSuggestionDesc: string
    meetingSuggestionDesc: string
    interviewTranscriptionDesc: string
    meetingTranscriptionDesc: string
    realTimeTranscription: string
    listening: string
    currentTranscription: string
    transcriptionHistory: string
    waitingForAudio: string
    listeningAudio: string
    assistantReady: string
    interviewReady: string
    meetingReady: string
  }

  // 权限页面
  permissions: {
    title: string
    refresh: string
    screenRecording: string
    screenRecordingDesc: string
    microphone: string
    microphoneDesc: string
    apiKey: string
    apiKeyDesc: string
    audioDevice: string
    audioDeviceDesc: string
    granted: string
    denied: string
    configured: string
    notConfigured: string
    normal: string
    abnormal: string
    openSettings: string
    requestPermission: string
    configureAPI: string
    test: string
    settings: string
    tip: string
    tipMessage: string
  }
}

export const languages: Record<string, LanguageConfig> = {
  zh: {
    common: {
      settings: '设置',
      permissions: '权限',
      account: '账户',
      close: '关闭',
      back: '返回',
      connected: '已连接',
      disconnected: '未连接',
      service: '服务'
    },
    main: {
      interviewAssistant: 'AI 面试助手',
      interviewDescription: '智能面试模拟与实时建议',
      startInterview: '开始面试',
      meetingAssistant: 'AI 会议助手',
      meetingDescription: '智能会议记录与回复建议',
      startMeeting: '开始会议',
      resumeManagement: '简历管理',
      uploadResume: '上传简历',
      manualInput: '手动输入',
      interviewPreparation: '面试准备',
      newPreparation: '新建准备项'
    },
    collaboration: {
      aiSuggestions: 'AI 智能建议',
      interviewSuggestionDesc: '基于面试问题的回答建议',
      meetingSuggestionDesc: '基于会议内容的回复建议',
      interviewTranscriptionDesc: '根据面试内容进行实时转录',
      meetingTranscriptionDesc: '根据会议内容进行实时转录',
      realTimeTranscription: '实时转录',
      listening: '正在监听',
      currentTranscription: '当前转录',
      transcriptionHistory: '转录历史',
      waitingForAudio: '等待音频输入',
      listeningAudio: '正在聆听音频...',
      assistantReady: 'AI 助手准备就绪',
      interviewReady: '面试助手准备就绪',
      meetingReady: '会议助手准备就绪'
    },
    permissions: {
      title: '系统权限状态',
      refresh: '刷新',
      screenRecording: '屏幕录制权限',
      screenRecordingDesc: '系统音频捕获',
      microphone: '麦克风权限',
      microphoneDesc: '语音输入录制',
      apiKey: 'API密钥配置',
      apiKeyDesc: 'Gemini API状态',
      audioDevice: '音频设备状态',
      audioDeviceDesc: '系统音频工具',
      granted: '✅',
      denied: '❌',
      configured: '✅',
      notConfigured: '⚠️',
      normal: '✅',
      abnormal: '❌',
      openSettings: '打开设置',
      requestPermission: '请求权限',
      configureAPI: '配置API',
      test: '测试',
      settings: '设置',
      tip: '提示：',
      tipMessage: '屏幕录制用于系统音频捕获，麦克风用于语音输入，API密钥启用AI功能，音频设备确保工具正常运行'
    }
  },
  en: {
    common: {
      settings: 'Settings',
      permissions: 'Permissions',
      account: 'Account',
      close: 'Close',
      back: 'Back',
      connected: 'Connected',
      disconnected: 'Disconnected',
      service: 'Service'
    },
    main: {
      interviewAssistant: 'AI Interview Assistant',
      interviewDescription: 'Smart interview simulation with real-time suggestions',
      startInterview: 'Start Interview',
      meetingAssistant: 'AI Meeting Assistant',
      meetingDescription: 'Smart meeting recording with reply suggestions',
      startMeeting: 'Start Meeting',
      resumeManagement: 'Resume Management',
      uploadResume: 'Upload Resume',
      manualInput: 'Manual Input',
      interviewPreparation: 'Interview Preparation',
      newPreparation: 'New Preparation'
    },
    collaboration: {
      aiSuggestions: 'AI Smart Suggestions',
      interviewSuggestionDesc: 'Answer suggestions based on interview questions',
      meetingSuggestionDesc: 'Reply suggestions based on meeting content',
      interviewTranscriptionDesc: 'Real-time transcription based on interview content',
      meetingTranscriptionDesc: 'Real-time transcription based on meeting content',
      realTimeTranscription: 'Real-time Transcription',
      listening: 'Listening',
      currentTranscription: 'Current Transcription',
      transcriptionHistory: 'Transcription History',
      waitingForAudio: 'Waiting for audio input',
      listeningAudio: 'Listening to audio...',
      assistantReady: 'AI Assistant Ready',
      interviewReady: 'Interview Assistant Ready',
      meetingReady: 'Meeting Assistant Ready'
    },
    permissions: {
      title: 'System Permissions Status',
      refresh: 'Refresh',
      screenRecording: 'Screen Recording Permission',
      screenRecordingDesc: 'System Audio Capture',
      microphone: 'Microphone Permission',
      microphoneDesc: 'Voice Input Recording',
      apiKey: 'API Key Configuration',
      apiKeyDesc: 'Gemini API Status',
      audioDevice: 'Audio Device Status',
      audioDeviceDesc: 'System Audio Tool',
      granted: '✅',
      denied: '❌',
      configured: '✅',
      notConfigured: '⚠️',
      normal: '✅',
      abnormal: '❌',
      openSettings: 'Open Settings',
      requestPermission: 'Request Permission',
      configureAPI: 'Configure API',
      test: 'Test',
      settings: 'Settings',
      tip: 'Tip:',
      tipMessage: 'Screen recording is for system audio capture, microphone for voice input, API key enables AI features, audio device ensures tool functionality'
    }
  }
}

export const getLanguageConfig = (language: string): LanguageConfig => {
  return languages[language] || languages.zh
}
