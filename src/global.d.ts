// Global type definitions applicable across renderer and main processes
// Extend the Window interface so TypeScript recognizes the preload-exposed API
// and avoids "Property 'geekAssistant' does not exist on type Window" errors.

// NOTE: Keep in sync with the interface declared in `src/preload/index.ts`.
// For brevity (and to avoid deep import cycles), we make geekAssistant type `any`.
// You may replace `any` with the full `GeekAssistantAPI` type if needed.

export {}; // Ensure this file is treated as a module

declare global {
  interface Window {
    // Preload-exposed API for renderer processes
    geekAssistant: any;
    // Additional values exposed via `contextBridge.exposeInMainWorld('env', ...)`
    env?: Record<string, string>;
  }
}
