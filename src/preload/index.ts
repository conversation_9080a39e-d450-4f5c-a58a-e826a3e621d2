import { contextBridge, ipc<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron'

// 全局变量存储回调函数
let interviewOCRCallback: ((data: any) => void) | null = null

// 设置面试OCR监听器
ipcRenderer.on('interview-ocr-result', (_event: IpcRendererEvent, data: any) => {
  console.log('🎯 Received interview OCR result in preload:', data)

  // 调用React回调函数
  if (interviewOCRCallback) {
    console.log('🎯 Calling React callback function')
    interviewOCRCallback(data)
  } else {
    console.warn('⚠️ No React callback function registered')
  }
})

// GeekAssistant API接口
interface GeekAssistantAPI {
  // 窗口管理
  createFloatingWindow: () => Promise<boolean>
  closeFloatingWindow: () => Promise<boolean>
  enterCollaborationMode: () => Promise<boolean>
  exitCollaborationMode: () => Promise<boolean>

  // 隐身模式控制
  getStealthStatus: () => Promise<any>
  toggleMainWindowStealth: () => Promise<boolean>
  toggleFloatingWindowStealth: () => Promise<boolean>
  toggleOverallVisibility: () => Promise<boolean>
  toggleSuperStealthMode: () => Promise<boolean>

  // 反检测功能
  getAntiDetectionStatus: () => Promise<any>
  toggleMousePassthrough: () => Promise<boolean>
  toggleFloatingMousePassthrough: () => Promise<boolean>

  // 性能和安全监控
  getPerformanceReport: () => Promise<any>
  getOptimizationSuggestions: () => Promise<string[]>
  getSecurityReport: () => Promise<any>
  getCompatibilityInfo: () => Promise<any>

  // 高级窗口隐藏
  applyReverseAnalysisStealth: () => Promise<boolean>
  restoreWindowVisibility: () => Promise<boolean>
  getHiddenWindowsStatus: () => Promise<{ count: number; windowIds: number[] }>

  // 全局事件保护
  getGlobalEventProtectionStatus: () => Promise<any>
  stopKeyboardProtection: () => Promise<boolean>
  stopMouseProtection: () => Promise<boolean>

  // reverse-analysis鼠标穿透控制
  enableClickThrough: () => Promise<{ success?: boolean; error?: string }>
  disableClickThrough: () => Promise<{ success?: boolean; error?: string }>

  // reverse-analysis屏幕共享保护
  enableContentProtection: () => Promise<{ success?: boolean; error?: string }>
  disableContentProtection: () => Promise<{ success?: boolean; error?: string }>
  getContentProtectionStatus: () => Promise<{ enabled: boolean; supported: boolean }>

  // 事件监听
  onMousePassthroughChanged: (callback: (enabled: boolean) => void) => () => void

  // 截图测试功能
  testScreenshotRecognition: () => Promise<any>
  testImageFile: (imagePath: string) => Promise<any>
  testAreaScreenshot: () => Promise<any>
  testLocalOCR: () => Promise<any>
  testLocalOCRArea: () => Promise<any>

  // 面试OCR功能
  interviewOCRFullscreen: () => Promise<any>
  interviewOCRArea: () => Promise<any>
  onInterviewOCRResult: (callback: (data: any) => void) => void
  removeInterviewOCRListener: () => void
  onTestIPC: (callback: (data: any) => void) => void
  testIPCCommunication: () => Promise<any>

  // 面试上下文管理
  setInterviewContext: (preparation: any) => Promise<boolean>
  getInterviewContext: () => Promise<any>

  // 服务管理
  initializeServices: () => Promise<boolean>
  switchService: (config: any) => Promise<boolean>
  startSession: () => Promise<boolean>
  stopSession: () => Promise<boolean>
  updateTranscriptionLanguage: (language: string) => Promise<boolean>

  // 音频管理
  startAudioCapture: () => Promise<boolean>
  stopAudioCapture: () => Promise<boolean>

  // 传统Gemini API（向后兼容）
  initializeGemini: (apiKey: string, customPrompt?: string, profile?: string, language?: string) => Promise<boolean>
  reconnectGemini: () => Promise<boolean>
  manualReconnect: () => Promise<boolean>
  disconnectGemini: () => Promise<boolean>

  // 权限管理
  checkPermissions: () => Promise<any>
  checkScreenRecordingPermission: () => Promise<any>
  checkMicrophonePermission: () => Promise<any>
  checkApiKeyStatus: () => Promise<any>
  checkAudioDeviceStatus: () => Promise<any>
  openSystemPreferences: (pane: string) => Promise<boolean>
  testAudioCapture: () => Promise<any>
  requestMicrophonePermission: () => Promise<any>

  // 配置管理
  getCurrentServiceConfig: () => Promise<any>
  updateServiceConfig: (config: any) => Promise<boolean>
  reloadServiceConfig: () => Promise<any>

  // 自定义API管理
  customAPI?: {
    getAll: () => Promise<any[]>
    add: (api: any) => Promise<boolean>
    update: (id: string, updates: any) => Promise<boolean>
    remove: (id: string) => Promise<boolean>
    testChat: (id: string, message: string) => Promise<any>
  }

  // 事件监听
  onStatusUpdate: (callback: (status: string) => void) => () => void
  onTranscriptionUpdate: (callback: (text: string) => void) => () => void
  onAIResponse: (callback: (response: any) => void) => () => void
  onSessionInitializing: (callback: (initializing: boolean) => void) => () => void
  onSessionError: (callback: (error: string) => void) => () => void
  onSessionClosed: (callback: () => void) => () => void
  onConfigUpdated: (callback: (config: any) => void) => () => void
}

// 暴露给渲染进程的API
const geekAssistantAPI: GeekAssistantAPI = {
  // 窗口管理
  createFloatingWindow: () => ipcRenderer.invoke('create-floating-window'),
  closeFloatingWindow: () => ipcRenderer.invoke('close-floating-window'),
  enterCollaborationMode: () => ipcRenderer.invoke('enter-collaboration-mode'),
  exitCollaborationMode: () => ipcRenderer.invoke('exit-collaboration-mode'),

  // 隐身模式控制
  getStealthStatus: () => ipcRenderer.invoke('get-stealth-status'),
  toggleMainWindowStealth: () => ipcRenderer.invoke('toggle-main-window-stealth'),
  toggleFloatingWindowStealth: () => ipcRenderer.invoke('toggle-floating-window-stealth'),
  toggleOverallVisibility: () => ipcRenderer.invoke('toggle-overall-visibility'),
  toggleSuperStealthMode: () => ipcRenderer.invoke('toggle-super-stealth-mode'),

  // 反检测功能
  getAntiDetectionStatus: () => ipcRenderer.invoke('get-anti-detection-status'),
  toggleMousePassthrough: () => ipcRenderer.invoke('toggle-mouse-passthrough'),
  toggleFloatingMousePassthrough: () => ipcRenderer.invoke('toggle-floating-mouse-passthrough'),

  // 性能和安全监控
  getPerformanceReport: () => ipcRenderer.invoke('get-performance-report'),
  getOptimizationSuggestions: () => ipcRenderer.invoke('get-optimization-suggestions'),
  getSecurityReport: () => ipcRenderer.invoke('get-security-report'),
  getCompatibilityInfo: () => ipcRenderer.invoke('get-compatibility-info'),

  // 高级窗口隐藏
  applyReverseAnalysisStealth: () => ipcRenderer.invoke('apply-reverse-analysis-stealth'),
  restoreWindowVisibility: () => ipcRenderer.invoke('restore-window-visibility'),
  getHiddenWindowsStatus: () => ipcRenderer.invoke('get-hidden-windows-status'),

  // 全局事件保护
  getGlobalEventProtectionStatus: () => ipcRenderer.invoke('get-global-event-protection-status'),
  stopKeyboardProtection: () => ipcRenderer.invoke('stop-keyboard-protection'),
  stopMouseProtection: () => ipcRenderer.invoke('stop-mouse-protection'),

  // reverse-analysis鼠标穿透控制
  enableClickThrough: () => ipcRenderer.invoke('enable-click-through'),
  disableClickThrough: () => ipcRenderer.invoke('disable-click-through'),

  // reverse-analysis屏幕共享保护
  enableContentProtection: () => ipcRenderer.invoke('enable-content-protection'),
  disableContentProtection: () => ipcRenderer.invoke('disable-content-protection'),
  getContentProtectionStatus: () => ipcRenderer.invoke('get-content-protection-status'),

  // 事件监听
  onMousePassthroughChanged: (callback: (enabled: boolean) => void) => {
    const handler = (_event: IpcRendererEvent, enabled: boolean) => callback(enabled);
    ipcRenderer.on('mouse-passthrough-changed', handler);
    return () => ipcRenderer.removeListener('mouse-passthrough-changed', handler);
  },

  // 截图测试功能
  testScreenshotRecognition: () => ipcRenderer.invoke('test-screenshot-recognition'),
  testImageFile: (imagePath: string) => ipcRenderer.invoke('test-image-file', imagePath),
  testAreaScreenshot: () => ipcRenderer.invoke('test-area-screenshot'),
  testLocalOCR: () => ipcRenderer.invoke('test-local-ocr'),
  testLocalOCRArea: () => ipcRenderer.invoke('test-local-ocr-area'),

  // 面试OCR功能
  interviewOCRFullscreen: () => ipcRenderer.invoke('interview-ocr-fullscreen'),
  interviewOCRArea: () => ipcRenderer.invoke('interview-ocr-area'),
  getOCRStatus: () => ipcRenderer.invoke('get-ocr-status'),
  diagnoseRemoteAPI: () => ipcRenderer.invoke('diagnose-remote-api'),

  // 监听面试OCR结果 - 注册回调函数
  onInterviewOCRResult: (callback: (data: any) => void) => {
    console.log('🎯 Registering interview OCR callback function')
    interviewOCRCallback = callback
  },
  removeInterviewOCRListener: () => {
    console.log('🎯 Removing interview OCR result listener in preload')
    interviewOCRCallback = null // just clear the callback to avoid removing IPC listener
  },

  // 测试IPC通信
  onTestIPC: (callback: (data: any) => void) => {
    console.log('🧪 Setting up test IPC listener in preload')
    ipcRenderer.on('test-ipc', (_event: IpcRendererEvent, data: any) => {
      console.log('🧪 Received test IPC in preload:', data)
      callback(data)
    })
  },
  testIPCCommunication: () => ipcRenderer.invoke('test-ipc-communication'),

  // 面试上下文管理
  setInterviewContext: (preparation: any) => ipcRenderer.invoke('set-interview-context', preparation),
  getInterviewContext: () => ipcRenderer.invoke('get-interview-context'),

  // 服务管理
  initializeServices: () => ipcRenderer.invoke('initialize-services'),
  switchService: (config: any) => ipcRenderer.invoke('switch-service', config),
  startSession: () => ipcRenderer.invoke('start-session'),
  stopSession: () => ipcRenderer.invoke('stop-session'),
  updateTranscriptionLanguage: (language: string) => ipcRenderer.invoke('update-transcription-language', language),

  // 音频管理
  startAudioCapture: () => ipcRenderer.invoke('start-audio-capture'),
  stopAudioCapture: () => ipcRenderer.invoke('stop-audio-capture'),

  // 配置管理
  getCurrentServiceConfig: () => ipcRenderer.invoke('get-current-service-config'),
  updateServiceConfig: (config: any) => ipcRenderer.invoke('update-service-config', config),
  reloadServiceConfig: () => ipcRenderer.invoke('reload-service-config'),

  // 自定义API管理
  customAPI: {
    getAll: () => ipcRenderer.invoke('custom-api-get-all'),
    add: (api: any) => ipcRenderer.invoke('custom-api-add', api),
    update: (id: string, updates: any) => ipcRenderer.invoke('custom-api-update', id, updates),
    remove: (id: string) => ipcRenderer.invoke('custom-api-remove', id),
    testChat: (id: string, message: string) => ipcRenderer.invoke('custom-api-test-chat', id, message)
  },

  // 传统Gemini API（向后兼容）
  initializeGemini: (apiKey, customPrompt = '', profile = 'interview', language = 'cmn-CN') =>
    ipcRenderer.invoke('initialize-gemini', apiKey, customPrompt, profile, language),

  // Gemini 连接管理
  reconnectGemini: () => ipcRenderer.invoke('reconnect-gemini'),
  manualReconnect: () => ipcRenderer.invoke('manual-reconnect'),
  disconnectGemini: () => ipcRenderer.invoke('disconnect-gemini'),

  // 权限管理
  checkPermissions: () => ipcRenderer.invoke('check-permissions'),
  checkScreenRecordingPermission: () => ipcRenderer.invoke('check-screen-recording-permission'),
  checkMicrophonePermission: () => ipcRenderer.invoke('check-microphone-permission'),
  checkApiKeyStatus: () => ipcRenderer.invoke('check-api-key-status'),
  checkAudioDeviceStatus: () => ipcRenderer.invoke('check-audio-device-status'),
  openSystemPreferences: (pane: string) => ipcRenderer.invoke('open-system-preferences', pane),
  testAudioCapture: () => ipcRenderer.invoke('test-audio-capture'),
  requestMicrophonePermission: () => ipcRenderer.invoke('request-microphone-permission'),

  // 事件监听
  onStatusUpdate: (callback) => {
    const listener = (_event: IpcRendererEvent, status: string) => callback(status)
    ipcRenderer.on('update-status', listener)
    return () => ipcRenderer.removeListener('update-status', listener)
  },
  
  onTranscriptionUpdate: (callback) => {
    const listener = (_event: IpcRendererEvent, text: string) => callback(text)
    ipcRenderer.on('transcription-update', listener)
    return () => ipcRenderer.removeListener('transcription-update', listener)
  },
  
  onAIResponse: (callback) => {
    const listener = (_event: IpcRendererEvent, response: any) => callback(response)
    ipcRenderer.on('ai-response', listener)
    return () => ipcRenderer.removeListener('ai-response', listener)
  },
  
  onSessionInitializing: (callback) => {
    const listener = (_event: IpcRendererEvent, initializing: boolean) => callback(initializing)
    ipcRenderer.on('session-initializing', listener)
    return () => ipcRenderer.removeListener('session-initializing', listener)
  },
  
  onSessionError: (callback) => {
    const listener = (_event: IpcRendererEvent, error: string) => callback(error)
    ipcRenderer.on('session-error', listener)
    return () => ipcRenderer.removeListener('session-error', listener)
  },
  
  onSessionClosed: (callback) => {
    const listener = () => callback()
    ipcRenderer.on('session-closed', listener)
    return () => ipcRenderer.removeListener('session-closed', listener)
  },

  onConfigUpdated: (callback) => {
    const listener = (_event: IpcRendererEvent, config: any) => callback(config)
    ipcRenderer.on('config-updated', listener)
    return () => ipcRenderer.removeListener('config-updated', listener)
  }
}

// 使用contextBridge暴露API
contextBridge.exposeInMainWorld('geekAssistant', geekAssistantAPI)

// 也可以暴露Node.js环境变量
contextBridge.exposeInMainWorld('env', {
  GEMINI_API_KEY: process.env.VITE_GEMINI_API_KEY,
  SUPABASE_URL: process.env.VITE_SUPABASE_URL,
  SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY,
  DEV_MODE: process.env.VITE_DEV_MODE
})
