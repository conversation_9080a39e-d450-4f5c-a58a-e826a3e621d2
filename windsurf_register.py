"""
自动完成 Windsurf 注册 + 邮箱验证 + 提取 Auth Token
依赖:
    pip install playwright==1.44.0
    playwright install chromium
运行:
    python windsurf_register.py
"""
import re, time, random, string
from playwright.sync_api import sync_playwright, TimeoutError as PWTimeout

AITRE_URL     = "https://mail.aitre.cc/zh-CN"
WINDSURF_REG  = "https://windsurf.com/account/register"
TOKEN_PAGE    = "https://windsurf.com/jetbrains-show-auth-token"

FIRST_NAME, LAST_NAME = "John", "Doe"
PWD = "P@" + "".join(random.choice(string.ascii_letters + string.digits) for _ in range(10))

def wait_verify_mail(mail_page, timeout_sec=200):
    """循环点击“刷新邮件”，直到捕获验证邮件并返回链接"""
    start = time.time()
    while time.time() - start < timeout_sec:
        mail_page.locator("button:has-text('刷新'), [data-test='refresh']").first.click()
        time.sleep(2)
        rows = mail_page.locator("table tbody tr")
        for i in range(rows.count()):
            t = rows.nth(i).inner_text().lower()
            if any(k in t for k in ("wind", "verify", "确认", "验证")):
                rows.nth(i).click()
                mail_page.wait_for_selector("iframe", timeout=10000)
                frame = mail_page.frame_locator("iframe").first
                body_html = frame.locator("body").inner_html()
                m = re.search(r"https://[^\"'<>\\s]+", body_html)
                if m:
                    return m.group(0)
        time.sleep(5)
    raise RuntimeError("验证邮件超时未收到")

def main():
    print("随机密码:", PWD)
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False, slow_mo=60)
        ctx = browser.new_context()
        # 1. 打开 aitre 生成邮箱
        mail = ctx.new_page()
        mail.goto(AITRE_URL, timeout=60000)
        mail.locator("button:has-text('生成新邮箱')").click()
        mail.wait_for_selector("text=@aitre.cc", timeout=30000)
        # 优先从只读输入框 value 里取邮箱，兼容站点布局变化
        EMAIL = None
        try:
            email_input = mail.locator("input[readonly]")
            if email_input.count() > 0:
                EMAIL = email_input.input_value().strip()
        except Exception:
            pass

        if not EMAIL:
            email_raw = mail.locator("text=@aitre.cc").first.inner_text()
            m = re.search(r"[A-Za-z0-9_.+-]+@aitre\.cc", email_raw)
            if m:
                EMAIL = m.group(0)

        if not EMAIL:
            raise RuntimeError("未能从 aitre 页面提取邮箱地址")
        print("临时邮箱:", EMAIL)
        # 2. Windsurf 注册
        ws = ctx.new_page()
        ws.goto(WINDSURF_REG, timeout=60000)
        ws.fill("input[placeholder='Your first name']", FIRST_NAME)
        ws.fill("input[placeholder='Your last name']", LAST_NAME)
        ws.fill("input[type='email']", EMAIL)
        ws.check("input[type='checkbox']")
        ws.click("button:has-text('Continue')")
        ws.wait_for_selector("input#password", timeout=30000)
        ws.fill("input#password", PWD)
        ws.fill("input#passwordConfirmation", PWD)
        ws.click("button:has-text('Continue')")
        print("等待验证邮件 …")
        verify_link = wait_verify_mail(mail)
        print("验证链接:", verify_link)
        ws.bring_to_front()
        ws.goto(verify_link, timeout=60000)
        ws.wait_for_load_state("networkidle")
        ws.goto(TOKEN_PAGE, timeout=60000)
        ws.wait_for_selector("text=Auth", timeout=30000)
        token_text = ws.locator("text=Auth").inner_text().strip()
        print("\n=== Windsurf Auth Token ===\n", token_text, "\n")
        input("已完成，按回车关闭浏览器…")
        browser.close()

if __name__ == "__main__":
    try:
        main()
    except PWTimeout as e:
        print("Playwright 超时:", e)
