{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "useUnknownInCatchVariables": false, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/renderer/src/*"], "@renderer/*": ["src/renderer/src/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["src/renderer/src/components/Resume*Modal.tsx", "src/renderer/src/components/**/Resume*Modal.tsx"]}